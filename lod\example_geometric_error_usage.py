"""
几何误差LOD系统使用示例
展示如何配置和使用重构后的LOD系统
"""

import sys
import os
from pxr import Gf

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入重构后的配置类
try:
    from simple_lod_example_standalone import StandaloneConfig
    print("✓ Successfully imported StandaloneConfig")
except ImportError as e:
    print(f"⚠️ Warning: Could not import StandaloneConfig: {e}")
    # 创建简化的配置类用于演示
    class StandaloneConfig:
        def __init__(self):
            self.usd_file_path = ""
            self.usdz_paths = {}
            self.camera_path = "/World/Camera"
            self.lod_geometric_errors = {}
            self.screen_error_thresholds = {}

def create_pixel_threshold_config():
    """创建使用像素阈值的配置"""
    config = StandaloneConfig()
    
    # 基本配置
    config.usd_file_path = "E:/wanleqi/LOD_Demo/lod-demo-block-0000.usd"
    config.usdz_paths = {
        "High": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/20.usdz",
        "Medium": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/18.usdz", 
        "Low": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/17.usdz"
    }
    
    # 几何误差配置（世界单位，米）
    config.lod_geometric_errors = {
        "High": 0.5,    # 高质量：0.5米几何误差
        "Medium": 2.0,  # 中质量：2.0米几何误差  
        "Low": 8.0      # 低质量：8.0米几何误差
    }
    
    # 像素阈值配置
    config.screen_error_thresholds = {
        "High": 2.0,    # 2像素
        "Medium": 8.0,  # 8像素
        "Low": 20.0     # 20像素
    }
    
    # 距离回退配置（保持向后兼容）
    config.distance_threshold_high = 50.0
    config.distance_threshold_medium = 100.0
    config.distance_threshold_low = 200.0
    
    return config

def create_ratio_threshold_config():
    """创建使用比例阈值的配置"""
    config = StandaloneConfig()
    
    # 基本配置
    config.usd_file_path = "E:/wanleqi/LOD_Demo/lod-demo-block-0000.usd"
    config.usdz_paths = {
        "High": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/20.usdz",
        "Medium": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/18.usdz", 
        "Low": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/17.usdz"
    }
    
    # 几何误差配置（世界单位，米）
    config.lod_geometric_errors = {
        "High": 0.3,    # 更高精度：0.3米几何误差
        "Medium": 1.5,  # 中等精度：1.5米几何误差  
        "Low": 6.0      # 较低精度：6.0米几何误差
    }
    
    # 比例阈值配置（屏幕高度百分比）
    config.screen_error_thresholds = {
        "High": 0.02,   # 2%屏幕高度
        "Medium": 0.08, # 8%屏幕高度
        "Low": 0.2      # 20%屏幕高度
    }
    
    # 距离回退配置
    config.distance_threshold_high = 30.0
    config.distance_threshold_medium = 80.0
    config.distance_threshold_low = 150.0
    
    return config

def create_mixed_threshold_config():
    """创建混合阈值的配置"""
    config = StandaloneConfig()
    
    # 基本配置
    config.usd_file_path = "E:/wanleqi/LOD_Demo/lod-demo-block-0000.usd"
    config.usdz_paths = {
        "High": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/20.usdz",
        "Medium": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/18.usdz", 
        "Low": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/17.usdz"
    }
    
    # 几何误差配置
    config.lod_geometric_errors = {
        "High": 0.4,    
        "Medium": 1.8,  
        "Low": 7.0      
    }
    
    # 混合阈值配置
    config.screen_error_thresholds = {
        "High": 0.015,  # 1.5%屏幕高度（比例）
        "Medium": 10.0, # 10像素（像素）
        "Low": 25.0     # 25像素（像素）
    }
    
    return config

def create_high_precision_config():
    """创建高精度应用的配置"""
    config = StandaloneConfig()
    
    # 基本配置
    config.usd_file_path = "E:/wanleqi/LOD_Demo/lod-demo-block-0000.usd"
    config.usdz_paths = {
        "High": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/20.usdz",
        "Medium": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/18.usdz", 
        "Low": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/17.usdz"
    }
    
    # 高精度几何误差配置
    config.lod_geometric_errors = {
        "High": 0.1,    # 极高精度：0.1米几何误差
        "Medium": 0.5,  # 高精度：0.5米几何误差  
        "Low": 2.0      # 中等精度：2.0米几何误差
    }
    
    # 严格的像素阈值
    config.screen_error_thresholds = {
        "High": 1.0,    # 1像素
        "Medium": 4.0,  # 4像素
        "Low": 10.0     # 10像素
    }
    
    # 更近的距离阈值
    config.distance_threshold_high = 20.0
    config.distance_threshold_medium = 50.0
    config.distance_threshold_low = 100.0
    
    return config

def print_config_summary(config, name):
    """打印配置摘要"""
    print(f"\n=== {name} ===")
    print(f"USD文件: {config.usd_file_path}")
    
    print(f"\n几何误差配置:")
    for lod, error in config.lod_geometric_errors.items():
        print(f"  {lod}: {error}m")
    
    print(f"\n屏幕误差阈值:")
    for lod, threshold in config.screen_error_thresholds.items():
        unit = "比例" if threshold <= 1.0 else "像素"
        if unit == "比例":
            print(f"  {lod}: {threshold:.3f} ({threshold*100:.1f}%屏幕高度)")
        else:
            print(f"  {lod}: {threshold}像素")
    
    if hasattr(config, 'distance_threshold_high'):
        print(f"\n距离回退阈值:")
        print(f"  High: {config.distance_threshold_high}m")
        print(f"  Medium: {config.distance_threshold_medium}m")
        print(f"  Low: {config.distance_threshold_low}m")

def usage_example():
    """使用示例"""
    print("=== 几何误差LOD系统配置示例 ===")
    
    # 1. 像素阈值配置（适合固定分辨率）
    pixel_config = create_pixel_threshold_config()
    print_config_summary(pixel_config, "像素阈值配置（固定分辨率应用）")
    
    # 2. 比例阈值配置（适合多分辨率）
    ratio_config = create_ratio_threshold_config()
    print_config_summary(ratio_config, "比例阈值配置（多分辨率应用）")
    
    # 3. 混合阈值配置
    mixed_config = create_mixed_threshold_config()
    print_config_summary(mixed_config, "混合阈值配置（灵活应用）")
    
    # 4. 高精度配置
    precision_config = create_high_precision_config()
    print_config_summary(precision_config, "高精度配置（精密应用）")
    
    print(f"\n=== 使用方法 ===")
    print("1. 选择合适的配置模板")
    print("2. 根据实际需求调整几何误差和阈值")
    print("3. 使用 run_standalone_mode(config) 启动系统")
    
    print(f"\n示例代码:")
    print("```python")
    print("# 创建配置")
    print("config = create_pixel_threshold_config()")
    print("")
    print("# 自定义配置")
    print("config.usd_file_path = 'your_scene.usd'")
    print("config.lod_geometric_errors['High'] = 0.3  # 调整几何误差")
    print("config.screen_error_thresholds['High'] = 1.5  # 调整阈值")
    print("")
    print("# 启动系统")
    print("from simple_lod_example_standalone import run_standalone_mode")
    print("result = run_standalone_mode(config)")
    print("```")

if __name__ == "__main__":
    usage_example()
    
    print(f"\n=== 配置建议 ===")
    print("✅ 像素阈值：适合桌面应用、固定分辨率")
    print("✅ 比例阈值：适合移动应用、多分辨率")
    print("✅ 混合阈值：适合复杂场景、特殊需求")
    print("✅ 高精度配置：适合CAD、医疗、科学可视化")
    
    print(f"\n=== 调优提示 ===")
    print("1. 几何误差应该反映模型的实际精度差异")
    print("2. 阈值设置要考虑用户体验和性能平衡")
    print("3. 距离回退确保在极端情况下的稳定性")
    print("4. 测试不同距离下的LOD切换效果")
    print("5. 根据实际场景调整参数")
