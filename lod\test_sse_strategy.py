"""
测试SSE策略
验证新的屏幕空间误差计算和LOD选择逻辑
"""

import math
import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def calculate_sse(geometric_error, distance_to_camera, screen_width=1920, h_fov=60.0):
    """计算屏幕空间误差(SSE)"""
    if distance_to_camera <= 0:
        return float('inf')
    
    # SSE公式：SSE = (geometricError × screenWidth) / (2 × distanceToCamera × tan(hFOV/2))
    sse = (geometric_error * screen_width) / (2 * distance_to_camera * math.tan(math.radians(h_fov / 2)))
    
    return sse

def calculate_distance_to_bounding_sphere(camera_pos, bbox_center, bbox_size):
    """计算相机到包围球最近点的距离"""
    # 计算包围球半径（使用包围盒的最大尺寸作为直径）
    radius = max(bbox_size[0], bbox_size[1], bbox_size[2]) / 2.0
    
    # 计算相机到包围球中心的距离
    center_distance = math.sqrt(
        (camera_pos[0] - bbox_center[0])**2 + 
        (camera_pos[1] - bbox_center[1])**2 + 
        (camera_pos[2] - bbox_center[2])**2
    )
    
    # 距离到包围球表面 = 中心距离 - 半径
    distance_to_surface = max(center_distance - radius, 0.1)
    
    return distance_to_surface

def select_lod_by_sse(lod_configs, distance_to_camera, maximum_screen_space_error=8.0, screen_width=1920, h_fov=60.0, verbose=False):
    """使用SSE误差控制进行LOD选择"""
    # LOD级别按从低细节到高细节排序
    lod_levels = ["Low", "Medium", "High"]
    
    lod_sse_info = {}
    selected_lod = "Low"  # 默认选择最低细节
    
    if verbose:
        print(f"SSE选择策略: 最大可接受误差 = {maximum_screen_space_error}px")
        print(f"距离到包围球表面: {distance_to_camera:.2f}m")
        print(f"屏幕参数: 宽度={screen_width}px, 水平FOV={h_fov}°")
    
    # 计算每个LOD的SSE
    for lod_name in lod_levels:
        geometric_error = lod_configs.get(lod_name, 1.0)
        sse = calculate_sse(geometric_error, distance_to_camera, screen_width, h_fov)
        
        lod_sse_info[lod_name] = {
            'geometric_error': geometric_error,
            'sse': sse,
            'acceptable': sse <= maximum_screen_space_error
        }
        
        if verbose:
            status = "✅可接受" if sse <= maximum_screen_space_error else "❌超阈值"
            print(f"  {lod_name}: 几何误差={geometric_error:.1f}m, SSE={sse:.2f}px {status}")
    
    # 选择策略：选择SSE不超过阈值且拥有最低几何误差（最高精度）的LOD
    # 从低细节到高细节遍历，选择最后一个满足条件的
    for lod_name in lod_levels:
        sse_info = lod_sse_info[lod_name]
        if sse_info['acceptable']:
            selected_lod = lod_name
            if verbose:
                print(f"  → 选择 {lod_name} LOD (SSE={sse_info['sse']:.2f}px ≤ {maximum_screen_space_error}px)")
        else:
            # 如果当前LOD超过阈值，停止尝试更高细节的层级
            if verbose:
                print(f"  → {lod_name} LOD超过阈值，停止尝试更高细节层级")
            break
    
    if verbose:
        print(f"最终选择: {selected_lod} LOD")
    
    return selected_lod, lod_sse_info

def test_sse_calculation():
    """测试SSE计算"""
    print("=== SSE计算测试 ===\n")
    
    # 测试参数
    geometric_error = 0.5  # 0.5米几何误差
    screen_width = 1920
    h_fov = 60.0
    
    print(f"几何误差: {geometric_error}m")
    print(f"屏幕宽度: {screen_width}px")
    print(f"水平FOV: {h_fov}°")
    print(f"公式: SSE = (geometricError × screenWidth) / (2 × distanceToCamera × tan(hFOV/2))")
    
    # 计算tan(hFOV/2)
    tan_half_fov = math.tan(math.radians(h_fov / 2))
    print(f"tan({h_fov/2}°) = {tan_half_fov:.4f}")
    
    print(f"\n{'距离(m)':<8} {'SSE(px)':<10} {'计算过程':<40}")
    print(f"{'-'*65}")
    
    distances = [10, 25, 50, 100, 200, 500]
    
    for distance in distances:
        sse = calculate_sse(geometric_error, distance, screen_width, h_fov)
        calculation = f"{geometric_error}×{screen_width}/(2×{distance}×{tan_half_fov:.3f})"
        print(f"{distance:<8} {sse:>8.2f} {calculation}")

def test_sse_strategy():
    """测试SSE策略"""
    print(f"\n\n=== SSE策略测试 ===\n")
    
    # LOD配置
    lod_configs = {
        "High": 0.5,    # 高质量：0.5米几何误差
        "Medium": 2.0,  # 中质量：2.0米几何误差  
        "Low": 8.0      # 低质量：8.0米几何误差
    }
    
    # SSE阈值
    maximum_sse = 8.0
    
    print("LOD配置:")
    for lod, error in lod_configs.items():
        print(f"  {lod}: {error}m几何误差")
    
    print(f"\n最大可接受SSE: {maximum_sse}px")
    
    print(f"\n{'距离(m)':<8} {'选择的LOD':<10} {'详细信息':<50}")
    print(f"{'-'*75}")
    
    test_distances = [10, 25, 50, 75, 100, 150, 200, 300, 500]
    
    for distance in test_distances:
        selected_lod, lod_sse_info = select_lod_by_sse(
            lod_configs, distance, maximum_sse, verbose=False
        )
        
        # 构建详细信息
        details = []
        for lod in ["Low", "Medium", "High"]:
            if lod in lod_sse_info:
                info = lod_sse_info[lod]
                status = "✅" if info['acceptable'] else "❌"
                details.append(f"{lod}:{info['sse']:.1f}px{status}")
        
        detail_str = " | ".join(details)
        print(f"{distance:<8} {selected_lod:<10} {detail_str}")

def test_bounding_sphere_distance():
    """测试包围球距离计算"""
    print(f"\n\n=== 包围球距离计算测试 ===\n")
    
    # 假设的包围盒
    bbox_center = [0, 0, 0]
    bbox_size = [100, 80, 60]  # 100m x 80m x 60m
    
    print(f"包围盒中心: {bbox_center}")
    print(f"包围盒尺寸: {bbox_size}")
    
    # 计算包围球半径
    radius = max(bbox_size) / 2.0
    print(f"包围球半径: {radius}m")
    
    print(f"\n{'相机位置':<20} {'中心距离(m)':<12} {'表面距离(m)':<12} {'说明':<20}")
    print(f"{'-'*70}")
    
    test_positions = [
        ([0, 0, 100], "正前方100m"),
        ([0, 0, 75], "正前方75m"),
        ([0, 0, 50], "正前方50m（球表面）"),
        ([0, 0, 25], "正前方25m（球内部）"),
        ([100, 0, 0], "右侧100m"),
        ([50, 50, 50], "对角线位置"),
    ]
    
    for camera_pos, description in test_positions:
        center_distance = math.sqrt(sum((camera_pos[i] - bbox_center[i])**2 for i in range(3)))
        surface_distance = calculate_distance_to_bounding_sphere(camera_pos, bbox_center, bbox_size)
        
        print(f"{str(camera_pos):<20} {center_distance:>10.1f} {surface_distance:>10.1f} {description}")

def test_different_sse_thresholds():
    """测试不同SSE阈值的效果"""
    print(f"\n\n=== 不同SSE阈值效果测试 ===\n")
    
    lod_configs = {"High": 0.5, "Medium": 2.0, "Low": 8.0}
    distance = 100  # 固定距离100m
    
    sse_thresholds = [2.0, 4.0, 8.0, 16.0, 32.0]
    
    print(f"固定距离: {distance}m")
    print(f"LOD几何误差: High=0.5m, Medium=2.0m, Low=8.0m")
    
    print(f"\n{'SSE阈值(px)':<12} {'选择的LOD':<10} {'各LOD的SSE':<40}")
    print(f"{'-'*70}")
    
    for threshold in sse_thresholds:
        selected_lod, lod_sse_info = select_lod_by_sse(
            lod_configs, distance, threshold, verbose=False
        )
        
        sse_values = []
        for lod in ["Low", "Medium", "High"]:
            if lod in lod_sse_info:
                sse_values.append(f"{lod}:{lod_sse_info[lod]['sse']:.1f}")
        
        sse_str = " | ".join(sse_values)
        print(f"{threshold:<12} {selected_lod:<10} {sse_str}")

if __name__ == "__main__":
    test_sse_calculation()
    test_sse_strategy()
    test_bounding_sphere_distance()
    test_different_sse_thresholds()
    
    print(f"\n\n=== SSE策略总结 ===")
    print("✅ SSE公式: SSE = (geometricError × screenWidth) / (2 × distanceToCamera × tan(hFOV/2))")
    print("✅ 选择策略: 从低细节到高细节，选择SSE不超过阈值的最高精度LOD")
    print("✅ 距离计算: 使用到包围球表面的距离，而不是到中心的距离")
    print("✅ 阈值控制: 通过调整maximum_screen_space_error控制LOD切换的敏感度")
    
    print(f"\n✅ 关键改进:")
    print("  - 使用标准的SSE计算公式")
    print("  - 考虑包围球半径，更准确的距离计算")
    print("  - 从低到高遍历LOD，选择最高可接受精度")
    print("  - 支持灵活的SSE阈值配置")
