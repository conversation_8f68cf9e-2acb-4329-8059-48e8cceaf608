"""
调试SSE计算
分析为什么LOD_Low的screen_error会这么大
"""

import math

def calculate_sse(geometric_error, distance_to_camera, screen_width=1920, h_fov=60.0):
    """计算屏幕空间误差(SSE)"""
    if distance_to_camera <= 0:
        return float('inf')
    
    # SSE公式：SSE = (geometricError × screenWidth) / (2 × distanceToCamera × tan(hFOV/2))
    sse = (geometric_error * screen_width) / (2 * distance_to_camera * math.tan(math.radians(h_fov / 2)))
    
    return sse

def calculate_distance_to_bounding_sphere(camera_pos, bbox_center, bbox_size):
    """计算相机到包围球最近点的距离"""
    # 计算包围球半径（使用包围盒的最大尺寸作为直径）
    radius = max(bbox_size[0], bbox_size[1], bbox_size[2]) / 2.0
    
    # 计算相机到包围球中心的距离
    center_distance = math.sqrt(
        (camera_pos[0] - bbox_center[0])**2 + 
        (camera_pos[1] - bbox_center[1])**2 + 
        (camera_pos[2] - bbox_center[2])**2
    )
    
    # 距离到包围球表面 = 中心距离 - 半径
    distance_to_surface = max(center_distance - radius, 0.1)
    
    return distance_to_surface, center_distance, radius

def debug_sse_calculation():
    """调试SSE计算"""
    print("=== 调试SSE计算 ===\n")
    
    # 当前配置（根据用户修改）
    lod_configs = {
        "High": 1.0,    # 修改后：1.0米几何误差
        "Medium": 4.0,  # 修改后：4.0米几何误差  
        "Low": 8.0      # 8.0米几何误差
    }
    
    # 假设的场景参数
    bbox_center = [0, 0, 0]
    bbox_size = [200, 200, 200]  # 假设一个200m的包围盒
    
    # 测试距离
    test_distances = [47.7, 122]  # 用户提到的距离
    
    print("配置信息:")
    print(f"LOD几何误差: {lod_configs}")
    print(f"包围盒尺寸: {bbox_size}")
    print(f"包围球半径: {max(bbox_size)/2.0}m")
    print(f"屏幕宽度: 1920px")
    print(f"水平FOV: 60°")
    
    print(f"\n{'='*80}")
    print(f"{'距离(m)':<10} {'到表面距离':<12} {'High SSE':<12} {'Medium SSE':<12} {'Low SSE':<12}")
    print(f"{'='*80}")
    
    for center_distance in test_distances:
        # 模拟相机位置
        camera_pos = [0, 0, center_distance]
        
        # 计算到包围球表面的距离
        surface_distance, actual_center_dist, radius = calculate_distance_to_bounding_sphere(
            camera_pos, bbox_center, bbox_size
        )
        
        # 计算各LOD的SSE
        high_sse = calculate_sse(lod_configs["High"], surface_distance)
        medium_sse = calculate_sse(lod_configs["Medium"], surface_distance)
        low_sse = calculate_sse(lod_configs["Low"], surface_distance)
        
        print(f"{center_distance:<10} {surface_distance:<12.1f} {high_sse:<12.1f} {medium_sse:<12.1f} {low_sse:<12.1f}")
        
        # 详细分析
        print(f"  详细计算 (距离{center_distance}m):")
        print(f"    中心距离: {actual_center_dist:.1f}m")
        print(f"    包围球半径: {radius:.1f}m")
        print(f"    表面距离: {surface_distance:.1f}m")
        print(f"    Low LOD计算: {lod_configs['Low']} × 1920 / (2 × {surface_distance:.1f} × {math.tan(math.radians(30)):.3f}) = {low_sse:.1f}px")
        print()

def analyze_problem():
    """分析问题原因"""
    print("=== 问题分析 ===\n")
    
    print("可能的问题原因:")
    print("1. 包围球半径过大")
    print("   - 如果包围盒是200m×200m×200m，半径就是100m")
    print("   - 距离47.7m时，表面距离 = 47.7 - 100 = -52.3m → 被限制为0.1m")
    print("   - 这会导致SSE计算结果极大")
    
    print("\n2. 几何误差配置")
    print("   - Low LOD几何误差8.0m相对较大")
    print("   - 当距离很小时，SSE = 8.0 × 1920 / (2 × 0.1 × 0.577) ≈ 133,000px")
    
    print("\n3. 距离计算方法")
    print("   - 当前使用到包围球表面的距离")
    print("   - 如果相机在包围球内部或很近，距离会被限制为0.1m")
    print("   - 这是导致SSE值过大的主要原因")

def test_different_bbox_sizes():
    """测试不同包围盒尺寸的影响"""
    print("\n=== 不同包围盒尺寸的影响 ===\n")
    
    bbox_sizes = [
        [50, 50, 50],    # 小包围盒
        [100, 100, 100], # 中等包围盒
        [200, 200, 200], # 大包围盒
        [400, 400, 400], # 超大包围盒
    ]
    
    camera_distance = 47.7  # 固定相机距离
    low_geometric_error = 8.0
    
    print(f"固定相机距离: {camera_distance}m")
    print(f"Low LOD几何误差: {low_geometric_error}m")
    
    print(f"\n{'包围盒尺寸':<15} {'半径(m)':<10} {'表面距离(m)':<12} {'Low SSE(px)':<12}")
    print(f"{'-'*55}")
    
    for bbox_size in bbox_sizes:
        camera_pos = [0, 0, camera_distance]
        bbox_center = [0, 0, 0]
        
        surface_distance, center_dist, radius = calculate_distance_to_bounding_sphere(
            camera_pos, bbox_center, bbox_size
        )
        
        low_sse = calculate_sse(low_geometric_error, surface_distance)
        
        print(f"{str(bbox_size):<15} {radius:<10.1f} {surface_distance:<12.1f} {low_sse:<12.1f}")

def suggest_solutions():
    """建议解决方案"""
    print("\n=== 解决方案建议 ===\n")
    
    print("1. 调整距离计算方法:")
    print("   - 选项A: 使用到中心的距离，而不是到表面的距离")
    print("   - 选项B: 设置更合理的最小距离限制（如1.0m而不是0.1m）")
    print("   - 选项C: 使用混合距离计算（近距离用中心距离，远距离用表面距离）")
    
    print("\n2. 调整几何误差配置:")
    print("   - 减小Low LOD的几何误差（如从8.0m改为2.0m）")
    print("   - 调整各级别间的比例关系")
    
    print("\n3. 调整SSE阈值:")
    print("   - 增大maximum_screen_space_error（如从8.0px改为16.0px或32.0px）")
    print("   - 这样可以容忍更大的屏幕误差")
    
    print("\n4. 包围球计算优化:")
    print("   - 使用更精确的包围球计算")
    print("   - 考虑使用包围盒的对角线长度的一半作为半径")

if __name__ == "__main__":
    debug_sse_calculation()
    analyze_problem()
    test_different_bbox_sizes()
    suggest_solutions()
    
    print(f"\n{'='*60}")
    print("🔍 问题根源:")
    print("当相机距离包围球中心47.7m，但包围球半径可能是100m时，")
    print("表面距离 = 47.7 - 100 = -52.3m → 被限制为0.1m")
    print("导致SSE = 8.0 × 1920 / (2 × 0.1 × 0.577) ≈ 133,000px")
    
    print(f"\n💡 快速修复:")
    print("1. 使用到中心的距离: distance = center_distance")
    print("2. 或者设置合理的最小距离: max(surface_distance, 1.0)")
    print("3. 或者减小几何误差: Low LOD从8.0m改为2.0m")
