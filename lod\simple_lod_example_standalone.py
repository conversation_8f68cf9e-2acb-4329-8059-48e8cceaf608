"""
简单LOD示例 - Standalone版本
只加载一个区域，根据相机距离加载不同的LOD级别
通过控制visibility实现LOD切换
无需交互式输入，直接运行
支持Isaac Sim SimulationApp和本地USD文件加载
"""

import asyncio
import time
import math
import sys
import os

# Isaac Sim standalone 支持
from isaacsim import SimulationApp
# HUD 配置标志位
DISP_FPS        = 1 << 0
DISP_RESOLUTION = 1 << 3
DISP_DEV_MEM    = 1 << 13
DISP_HOST_MEM   = 1 << 14

config = {
    "width": 1280,
    "height": 720,
    "headless": False,
    "display_options": DISP_FPS | DISP_RESOLUTION | DISP_DEV_MEM | DISP_HOST_MEM,
}

simulation_app = SimulationApp(launch_config=config)

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from pxr import Usd, UsdGeom, Gf, Sdf
import omni.usd
import omni.kit.app
import omni.timeline
import omni.kit.commands

# 导入LOD调度器
try:
    from lod_scheduler import LODScheduler, BoundingBox, LODLevel, LODTile
    print("✓ Successfully imported lod_scheduler")
except ImportError as e:
    print(f"⚠️ Warning: Could not import lod_scheduler: {e}")
    print("Will use built-in LOD classes instead")
    
    # 如果导入失败，使用内置类定义
    from dataclasses import dataclass
    from enum import Enum
    
    class LODLevel(Enum):
        HIGH = "High"
        MEDIUM = "Medium"
        LOW = "Low"
        VERY_LOW = "Very_Low"
    
    @dataclass
    class BoundingBox:
        min_point: Gf.Vec3f
        max_point: Gf.Vec3f
        
        @property
        def center(self):
            return (self.min_point + self.max_point) * 0.5
        
        @property
        def size(self):
            return self.max_point - self.min_point
    
    @dataclass
    class LODTile:
        id: str
        bounding_box: BoundingBox
        lod_level: LODLevel
        usdz_path: str
        screen_error: float
        distance_threshold: float
    
    class LODScheduler:
        def __init__(self, stage, camera_path="/World/Camera"):
            self.stage = stage
            self.camera_path = camera_path
            self.lod_tiles = {}
        
        def get_camera_position(self):
            camera = self.stage.GetPrimAtPath(self.camera_path)
            if camera:
                xformable = UsdGeom.Xformable(camera)
                transform = xformable.GetLocalTransformation()
                return Gf.Vec3f(transform.ExtractTranslation())
            return None
        
        def calculate_distance_to_camera(self, bbox, camera_pos):
            center = bbox.center
            return math.sqrt(
                (camera_pos[0] - center[0])**2 + 
                (camera_pos[1] - center[1])**2 + 
                (camera_pos[2] - center[2])**2
            )
        
        def calculate_screen_error(self, geometric_error, camera_pos, target_pos=None, fov=60.0, screen_height=1080):
            """
            计算几何误差投影到屏幕的像素误差

            Args:
                geometric_error: 几何误差（世界单位，米）
                camera_pos: 相机位置
                target_pos: 目标位置（如果为None，使用bbox中心）
                fov: 视场角（度）
                screen_height: 屏幕高度（像素）

            Returns:
                screen_error: 屏幕误差（像素）
            """
            if target_pos is None:
                # 如果没有提供目标位置，使用相机位置（距离为0时返回无穷大）
                distance = 0
            else:
                distance = math.sqrt(
                    (camera_pos[0] - target_pos[0])**2 +
                    (camera_pos[1] - target_pos[1])**2 +
                    (camera_pos[2] - target_pos[2])**2
                )

            if distance <= 0:
                return float('inf')

            # 计算焦距（像素单位）
            focal_len_px = (screen_height / 2) / math.tan(math.radians(fov / 2))

            # 几何误差投影到屏幕的像素误差
            screen_error = (geometric_error / distance) * focal_len_px
            return screen_error
        
        def determine_lod_level(self, screen_error, distance):
            if screen_error <= 2.0 or distance <= 50.0:
                return LODLevel.HIGH
            elif screen_error <= 8.0 or distance <= 100.0:
                return LODLevel.MEDIUM
            elif screen_error <= 20.0 or distance <= 200.0:
                return LODLevel.LOW
            else:
                return LODLevel.VERY_LOW
        
        def build_octree_from_stage(self, max_depth=4, min_area_threshold=100.0):
            print("Building octree from stage...")
            # 简化的八叉树构建
            pass
        
        def update_lod_visibility(self):
            print("Updating LOD visibility...")
            # 简化的LOD可见性更新
            pass

# 配置类
class StandaloneConfig:
    """Standalone模式配置类"""
    def __init__(self):
        # USD文件配置
        self.usd_file_path = "C:/test-usd-path"  # 默认路径，需要用户修改
        
        # LOD配置
        self.usdz_paths = {
            "High": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/20.usdz",
            "Medium": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/18.usdz", 
            "Low": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/17.usdz"
        }
        
        # 相机配置
        self.camera_path = "/World/Camera"
        
        # LOD切换配置（距离与屏幕误差阈值，可调优）
        self.distance_threshold_high = 50.0   # 高LOD距离阈值
        self.distance_threshold_medium = 100.0  # 中LOD距离阈值
        self.distance_threshold_low = 200.0   # 低LOD距离阈值
        self.screen_error_threshold_high = 2.0
        self.screen_error_threshold_medium = 8.0
        self.screen_error_threshold_low = 20.0
        self.auto_mode = "movement"  # "movement" 或 "timer"
        self.timer_interval = 1.0  # 主线程事件订阅间隔（秒）
        self.debug_info = True  # 是否输出调试信息
        
        # 运行时控制
        self.auto_start_runtime = True  # 是否自动开启运行时
        
        # 相机移动模拟配置
        self.camera_start_position = Gf.Vec3f(0, 0, 50)  # 相机起始位置（更近，确保High LOD）
        self.camera_target_position = Gf.Vec3f(20, 0, 100)  # 相机目标位置（更远，测试LOD切换）
        self.camera_movement_duration = 30.0  # 移动持续时间（秒）
        self.camera_movement_loop = True  # 是否循环移动
        # 每个LOD的几何误差（世界单位，米）
        # 这些值表示每个LOD级别允许的最大几何误差，用于计算屏幕误差
        # 较小的几何误差对应更高质量的LOD
        self.lod_geometric_errors = {
            "High": 0.5,    # 高质量LOD：0.5米几何误差
            "Medium": 2.0,  # 中质量LOD：2.0米几何误差
            "Low": 8.0      # 低质量LOD：8.0米几何误差
        }

        # 屏幕误差阈值（像素或比例）
        # 支持两种单位：
        # - 像素阈值（>1，如 2/8/20）：按像素比较
        # - 比例阈值（<=1，如 0.02/0.08/0.2）：按屏幕高度比例比较
        self.screen_error_thresholds = {
            "High": 2.0,    # 2像素或0.02比例
            "Medium": 8.0,  # 8像素或0.08比例
            "Low": 20.0     # 20像素或0.2比例
        }

# 全局变量来跟踪当前LOD状态
current_lod_state = {"last_lod": None, "last_distance": None}
# 全局变量来跟踪实时更新状态
realtime_update_active = False
realtime_update_data = None
# 全局变量来跟踪自动更新订阅
auto_update_subscription = None

def load_usd_stage(usd_file_path):
    """加载USD文件到stage"""
    try:
        print(f"Loading USD file: {usd_file_path}")
        
        # 检查文件是否存在
        import os
        if not os.path.exists(usd_file_path):
            print(f"ERROR: USD file not found: {usd_file_path}")
            return False
        
        # 使用与 auto_camera_optimizer_standalone.py 相同的方法打开stage
        print(f"Opening stage: '{usd_file_path}'")
        omni.usd.get_context().open_stage(usd_file_path)
        stage = omni.usd.get_context().get_stage()
        
        if not stage:
            print(f"ERROR: Failed to open USD file: {usd_file_path}")
            return False
        
        print(f"Successfully loaded USD file: {usd_file_path}")
        return True
        
    except Exception as e:
        print(f"ERROR: Failed to load USD file: {e}")
        return False

def start_runtime():
    """启动运行时（播放模式）"""
    try:
        timeline = omni.timeline.get_timeline_interface()
        timeline.play()
        print("Runtime started (Play mode)")
        return True
    except Exception as e:
        print(f"ERROR: Failed to start runtime: {e}")
        return False

def stop_runtime():
    """停止运行时"""
    try:
        timeline = omni.timeline.get_timeline_interface()
        timeline.stop()
        print("Runtime stopped")
        return True
    except Exception as e:
        print(f"ERROR: Failed to stop runtime: {e}")
        return False

def wait_for_render_initialization(max_wait_time=10.0):
    """等待渲染系统初始化完成"""
    print("Waiting for render system initialization...")
    
    import time
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        # 更新 Isaac Sim 以推进渲染初始化
        simulation_app.update()
        
        # 检查是否有可用的 stage
        stage = omni.usd.get_context().get_stage()
        if stage:
            # 检查是否有 nurec prim
            nurec_prims = []
            for prim in stage.Traverse():
                attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
                attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")
                
                if not attr_min or not attr_max:
                    continue
                nurec_prims.append(prim)
            
            if nurec_prims:
                print(f"Found {len(nurec_prims)} NurecPrim(s), render system ready")
                return True
            else:
                print("Stage loaded but no NurecPrim found, continuing to wait...")
        
        time.sleep(0.1)  # 短暂等待
    
    print(f"Warning: Render initialization timeout after {max_wait_time}s")
    return False

def transform_point(p, transform_matrix):
    """
    变换点的函数，应用坐标系修正
    """
    point4 = Gf.Vec4d(p[0], p[1], p[2], 1.0)
    # 使用正确的Transform方法 - 使用矩阵乘法
    transformed = transform_matrix * point4
    return Gf.Vec3f(transformed[0], transformed[1], transformed[2])

def apply_coordinate_system_correction(minB, maxB, debug_info=False):
    """
    应用坐标系修正到边界框
    """
    if debug_info:
        print(f"  Original bounds: {minB} to {maxB}")
    
    # 应用 (90, 180, 0) 旋转的逆变换到坐标点
    # 这相当于将 NUREC 坐标系转换为 USD 坐标系
    
    # 创建旋转矩阵：先绕 Z 轴旋转 180°，再绕 X 轴旋转 90°
    # 注意：我们需要应用逆变换，所以是 (90, -180, 0) 的变换
    rot_z_180 = Gf.Matrix4d().SetRotate(Gf.Rotation(Gf.Vec3d(0, 0, 1), -180))
    rot_x_90 = Gf.Matrix4d().SetRotate(Gf.Rotation(Gf.Vec3d(1, 0, 0), 90))
    transform_matrix = rot_z_180 * rot_x_90

    # 重新计算变换后的最小最大值
    all_transformed_points = [
        transform_point(Gf.Vec3f(minB[0], minB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], minB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], maxB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(minB[0], maxB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(minB[0], minB[1], maxB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], minB[1], maxB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], maxB[1], maxB[2]), transform_matrix),
        transform_point(Gf.Vec3f(minB[0], maxB[1], maxB[2]), transform_matrix)
    ]

    # 计算变换后的实际边界
    min_x = min(p[0] for p in all_transformed_points)
    max_x = max(p[0] for p in all_transformed_points)
    min_y = min(p[1] for p in all_transformed_points)
    max_y = max(p[1] for p in all_transformed_points)
    min_z = min(p[2] for p in all_transformed_points)
    max_z = max(p[2] for p in all_transformed_points)

    corrected_minB = Gf.Vec3f(min_x, min_y, min_z)
    corrected_maxB = Gf.Vec3f(max_x, max_y, max_z)
    
    if debug_info:
        print(f"  Corrected bounds: {corrected_minB} to {corrected_maxB}")
    
    return corrected_minB, corrected_maxB

def get_nurec_bounds_from_stage():
    """从stage中获取NUREC prim的实际包围盒，应用坐标系修正"""
    stage = omni.usd.get_context().get_stage()
    
    print("Scanning stage for NUREC prims with bounds...")
    print("Applying coordinate system correction (90, 180, 0) rotation")
    print("Filtering to only process leaf nodes (no children with bounds)")
    
    min_point = Gf.Vec3f(float('inf'), float('inf'), float('inf'))
    max_point = Gf.Vec3f(float('-inf'), float('-inf'), float('-inf'))
    
    found_bounds = False
    nurec_prims = []
    
    # 首先收集所有有边界的prim
    prims_with_bounds = []
    for prim in stage.Traverse():
        attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
        attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")
        
        if attr_min and attr_max:
            prims_with_bounds.append(prim)
    
    # 然后过滤出叶子节点（没有子节点包含边界信息）
    leaf_prims = []
    for prim in prims_with_bounds:
        is_leaf = True
        prim_path = str(prim.GetPath())
        
        # 检查是否有子节点也包含边界信息
        for other_prim in prims_with_bounds:
            other_path = str(other_prim.GetPath())
            if other_path != prim_path and other_path.startswith(prim_path + "/"):
                is_leaf = False
                break
        
        if is_leaf:
            leaf_prims.append(prim)
    
    # 处理叶子节点
    for prim in leaf_prims:
        try:
            attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
            attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")
            
            min_bounds = Gf.Vec3f(*attr_min.Get())
            max_bounds = Gf.Vec3f(*attr_max.Get())
            
            # 应用坐标系修正
            corrected_min_bounds, corrected_max_bounds = apply_coordinate_system_correction(
                min_bounds, max_bounds, debug_info=True
            )
            
            nurec_prims.append({
                'path': str(prim.GetPath()),
                'min_bounds': corrected_min_bounds,
                'max_bounds': corrected_max_bounds,
                'original_min_bounds': min_bounds,
                'original_max_bounds': max_bounds
            })
            
            # 更新全局边界框（使用修正后的边界）
            min_point = Gf.Vec3f(
                min(min_point[0], corrected_min_bounds[0]),
                min(min_point[1], corrected_min_bounds[1]),
                min(min_point[2], corrected_min_bounds[2])
            )
            max_point = Gf.Vec3f(
                max(max_point[0], corrected_max_bounds[0]),
                max(max_point[1], corrected_max_bounds[1]),
                max(max_point[2], corrected_max_bounds[2])
            )
            found_bounds = True
            
            print(f"Found NUREC leaf prim: {prim.GetPath()}")
            print(f"  Original bounds: {min_bounds} to {max_bounds}")
            print(f"  Corrected bounds: {corrected_min_bounds} to {corrected_max_bounds}")
            
        except Exception as e:
            print(f"Warning: Failed to read bounds for {prim.GetPath()}: {e}")
    
    if not found_bounds:
        print("Warning: No NUREC leaf prims with bounds found in stage, using default bounds")
        min_point = Gf.Vec3f(-200, -200, -100)
        max_point = Gf.Vec3f(200, 200, 100)
        nurec_prims = []
    else:
        print(f"Found {len(nurec_prims)} NUREC leaf prims with bounds")
        print(f"Global corrected bounds: {min_point} to {max_point}")
    
    return BoundingBox(min_point, max_point), nurec_prims

def load_usdz_file_into_stage(usdz_path, target_path):
    """将USDZ文件加载到stage中的指定路径"""
    try:
        stage = omni.usd.get_context().get_stage()
        
        # 使用USD的SdfLayer来引用USDZ文件
        layer = Sdf.Layer.FindOrOpen(usdz_path)
        if layer:
            # 创建引用
            prim = stage.DefinePrim(target_path)
            prim.GetReferences().AddReference(usdz_path)
            print(f"Successfully referenced USDZ file: {usdz_path} at {target_path}")
            return True
        else:
            print(f"Warning: Could not open USDZ file: {usdz_path}")
            return False
    except Exception as e:
        print(f"Error loading USDZ file {usdz_path}: {e}")
        return False

def create_single_region_scene_with_usdz(usdz_paths, config: 'StandaloneConfig' = None):
    """创建包含实际USDZ文件的单个区域场景；可写入每个LOD的自定义SSE阈值"""
    stage = omni.usd.get_context().get_stage()
    
    print("Creating single region scene with actual USDZ files...")
    
    # 从stage中获取实际的NUREC包围盒
    region_bounds, nurec_prims = get_nurec_bounds_from_stage()
    
    # 创建区域容器
    region_path = "/World/SingleRegion"
    region_prim = stage.DefinePrim(region_path)
    region_prim.SetTypeName("Xform")
    
    # 计算区域中心点
    center = region_bounds.center
    print(f"Region center: {center}")
    
    # 创建3个LOD级别并加载USDZ文件
    lod_levels = [
        {
            "name": "High",
            "usdz_path": usdz_paths["High"],
            "position": center,
            "visible": True  # 默认显示高质量
        },
        {
            "name": "Medium", 
            "usdz_path": usdz_paths["Medium"],
            "position": center,
            "visible": False
        },
        {
            "name": "Low",
            "usdz_path": usdz_paths["Low"],
            "position": center,
            "visible": False
        }
    ]
    
    for lod_info in lod_levels:
        # 检查是否已存在该LOD层级
        lod_path = f"{region_path}/LOD_{lod_info['name']}"
        existing_lod_prim = stage.GetPrimAtPath(lod_path)
        
        if existing_lod_prim and existing_lod_prim.IsValid():
            print(f"LOD_{lod_info['name']} already exists at {lod_path}, skipping creation")
            continue
        
        # 创建LOD层级容器
        lod_prim = stage.DefinePrim(lod_path)
        lod_prim.SetTypeName("Xform")
        
        # 设置位置 - 修复：检查是否已存在transform操作
        xformable = UsdGeom.Xformable(lod_prim)
        
        # 检查是否已经有translate操作
        existing_ops = xformable.GetOrderedXformOps()
        has_translate = any(op.GetOpType() == UsdGeom.XformOp.TypeTranslate for op in existing_ops)
        
        if not has_translate:
            # 只有在没有translate操作时才添加
            xformable.AddTranslateOp().Set(lod_info['position'])
        else:
            # 如果已存在，则更新现有的translate操作
            for op in existing_ops:
                if op.GetOpType() == UsdGeom.XformOp.TypeTranslate:
                    op.Set(lod_info['position'])
                    break
        
        # 设置边界属性与LOD级别（使用修正后的坐标系）
        lod_prim.CreateAttribute("omni:nurec:crop:minBounds", Sdf.ValueTypeNames.Float3).Set(region_bounds.min_point)
        lod_prim.CreateAttribute("omni:nurec:crop:maxBounds", Sdf.ValueTypeNames.Float3).Set(region_bounds.max_point)
        lod_prim.CreateAttribute("omni:nurec:lod:level", Sdf.ValueTypeNames.String).Set(lod_info['name'])
        # 写入几何误差和屏幕误差阈值
        try:
            # 几何误差（世界单位）
            if config and hasattr(config, 'lod_geometric_errors'):
                geometric_error = config.lod_geometric_errors.get(lod_info['name'], {"High": 0.5, "Medium": 2.0, "Low": 8.0}[lod_info['name']])
            else:
                geometric_error = {"High": 0.5, "Medium": 2.0, "Low": 8.0}[lod_info['name']]
            lod_prim.CreateAttribute("omni:nurec:lod:geometric_error", Sdf.ValueTypeNames.Float).Set(geometric_error)

            # 屏幕误差阈值
            if config and hasattr(config, 'screen_error_thresholds'):
                screen_threshold = config.screen_error_thresholds.get(lod_info['name'], {"High": 2.0, "Medium": 8.0, "Low": 20.0}[lod_info['name']])
            else:
                screen_threshold = {"High": 2.0, "Medium": 8.0, "Low": 20.0}[lod_info['name']]
            lod_prim.CreateAttribute("omni:nurec:lod:sse", Sdf.ValueTypeNames.Float).Set(screen_threshold)

        except Exception:
            pass
        
        # 加载USDZ文件
        usdz_loaded = load_usdz_file_into_stage(lod_info['usdz_path'], f"{lod_path}/USDZContent")
        
        if usdz_loaded:
            # 使用UsdGeom.Imageable设置初始可见性
            imageable = UsdGeom.Imageable(lod_prim)
            vis_attr = imageable.GetVisibilityAttr()
            vis_attr.Set(UsdGeom.Tokens.inherited if lod_info['visible'] else UsdGeom.Tokens.invisible)
            
            print(f"Created LOD_{lod_info['name']} with loaded USDZ at {lod_path}")
            print(f"  Corrected bounds: {region_bounds.min_point} to {region_bounds.max_point}")
        else:
            print(f"Failed to load USDZ for LOD_{lod_info['name']} at {lod_path}")
        
        print(f"  USDZ Path: {lod_info['usdz_path']}")
        print(f"  Position: {lod_info['position']}")
        print(f"  Visible: {lod_info['visible']}")
    
    # 创建相机
    camera_path = "/World/Camera"
    if not stage.GetPrimAtPath(camera_path):
        camera = UsdGeom.Camera.Define(stage, camera_path)
        # 设置更近的相机位置，确保加载LOD_High
        # 距离区域中心约50%的区域尺寸，确保在高LOD阈值内
        region_size = region_bounds.size
        max_dimension = max(region_size[0], region_size[1], region_size[2])
        camera_distance = max_dimension * 0.3  # 设置为区域最大尺寸的30%，确保在High LOD范围内
        
        camera_position = Gf.Vec3f(
            center[0], 
            center[1], 
            center[2] + camera_distance
        )
        
        # 设置相机位置 - 修复：检查是否已存在transform操作
        xformable = UsdGeom.Xformable(camera)
        existing_ops = xformable.GetOrderedXformOps()
        has_translate = any(op.GetOpType() == UsdGeom.XformOp.TypeTranslate for op in existing_ops)
        
        if not has_translate:
            xformable.AddTranslateOp().Set(camera_position)
        else:
            for op in existing_ops:
                if op.GetOpType() == UsdGeom.XformOp.TypeTranslate:
                    op.Set(camera_position)
                    break
        
        print(f"Created camera at {camera_path}")
        print(f"Camera position: {camera_position}")
        print(f"Distance to region center: {camera_distance:.1f} (High LOD threshold: {max_dimension * 0.5:.1f})")
    else:
        # 如果相机已存在，更新其位置
        camera = stage.GetPrimAtPath(camera_path)
        # 设置更近的相机位置，确保加载LOD_High
        region_size = region_bounds.size
        max_dimension = max(region_size[0], region_size[1], region_size[2])
        camera_distance = max_dimension * 0.3  # 设置为区域最大尺寸的30%，确保在High LOD范围内
        
        camera_position = Gf.Vec3f(
            center[0], 
            center[1], 
            center[2] + camera_distance
        )
        
        xformable = UsdGeom.Xformable(camera)
        existing_ops = xformable.GetOrderedXformOps()
        has_translate = any(op.GetOpType() == UsdGeom.XformOp.TypeTranslate for op in existing_ops)
        
        if not has_translate:
            xformable.AddTranslateOp().Set(camera_position)
        else:
            for op in existing_ops:
                if op.GetOpType() == UsdGeom.XformOp.TypeTranslate:
                    op.Set(camera_position)
                    break
        
        print(f"Updated existing camera at {camera_path}")
        print(f"Camera position: {camera_position}")
        print(f"Distance to region center: {camera_distance:.1f} (High LOD threshold: {max_dimension * 0.5:.1f})")
    
    print("Single region scene with USDZ files created successfully!")
    print(f"Region bounds: {region_bounds.min_point} to {region_bounds.max_point}")
    print(f"Region size: {region_bounds.size}")
    print(f"Region center: {center}")
    
    return stage, region_bounds

def calculate_screen_metric(geometric_error, distance, fov=60.0, screen_height=1080):
    """
    计算几何误差投影到屏幕的度量值

    Args:
        geometric_error: 几何误差（世界单位，米）
        distance: 到目标的距离（米）
        fov: 视场角（度）
        screen_height: 屏幕高度（像素）

    Returns:
        tuple: (screen_pixels, screen_ratio)
            - screen_pixels: 屏幕误差（像素）
            - screen_ratio: 屏幕误差占屏幕高度的比例
    """
    if distance <= 0:
        return float('inf'), float('inf')

    # 计算焦距（像素单位）
    focal_len_px = (screen_height / 2) / math.tan(math.radians(fov / 2))

    # 几何误差投影到屏幕的像素误差
    screen_pixels = (geometric_error / distance) * focal_len_px

    # 计算屏幕比例
    screen_ratio = screen_pixels / screen_height

    return screen_pixels, screen_ratio

def determine_lod_by_screen_metric(screen_pixels, screen_ratio, thresholds, verbose=False):
    """
    根据屏幕度量值确定LOD级别

    Args:
        screen_pixels: 屏幕误差（像素）
        screen_ratio: 屏幕误差占屏幕高度的比例
        thresholds: LOD阈值字典，格式：{"High": threshold, "Medium": threshold, "Low": threshold}
        verbose: 是否输出详细信息

    Returns:
        str: LOD级别 ("High", "Medium", "Low")
    """
    # 自动判断阈值单位（<=1 则按比例，否则按像素）
    def _get_metric_value(threshold_value):
        if threshold_value <= 1.0:
            # 比例阈值
            return screen_ratio
        else:
            # 像素阈值
            return screen_pixels

    # 获取各级别阈值
    high_threshold = thresholds.get("High", 2.0)
    medium_threshold = thresholds.get("Medium", 8.0)
    low_threshold = thresholds.get("Low", 20.0)

    # 获取对应的度量值（使用High LOD的单位类型）
    metric_value = _get_metric_value(high_threshold)

    # 正确的LOD判断逻辑：屏幕误差大于等于阈值时使用该LOD
    # 屏幕误差大 → 需要高质量LOD
    if metric_value >= high_threshold:
        return "High"
    elif metric_value >= medium_threshold:
        return "Medium"
    elif metric_value >= low_threshold:
        return "Low"
    else:
        # 屏幕误差很小，使用最低质量即可
        return "Low"

def update_lod_visibility_by_geometric_error(stage, region_bounds, usdz_paths, verbose=True, config: 'StandaloneConfig' = None):
    """根据几何误差和屏幕投影确定LOD级别"""
    if verbose:
        print("\n=== Updating LOD Visibility by Geometric Error ===")

    # 获取相机位置
    camera = stage.GetPrimAtPath("/World/Camera")
    if not camera:
        if verbose:
            print("Camera not found!")
        return

    # 获取相机位置
    xformable = UsdGeom.Xformable(camera)
    transform = xformable.GetLocalTransformation()
    camera_position = Gf.Vec3f(transform.ExtractTranslation())

    # 计算到区域中心的距离
    center = region_bounds.center
    distance = math.sqrt(
        (camera_position[0] - center[0])**2 +
        (camera_position[1] - center[1])**2 +
        (camera_position[2] - center[2])**2
    )

    # 获取几何误差配置
    def _get_geometric_error(lod_name: str) -> float:
        """获取LOD的几何误差（世界单位）"""
        if config and hasattr(config, 'lod_geometric_errors'):
            return config.lod_geometric_errors.get(lod_name, {"High": 0.5, "Medium": 2.0, "Low": 8.0}[lod_name])
        return {"High": 0.5, "Medium": 2.0, "Low": 8.0}[lod_name]

    def _get_screen_threshold(lod_name: str) -> float:
        """获取LOD的屏幕误差阈值"""
        if config and hasattr(config, 'screen_error_thresholds'):
            return config.screen_error_thresholds.get(lod_name, {"High": 2.0, "Medium": 8.0, "Low": 20.0}[lod_name])
        return {"High": 2.0, "Medium": 8.0, "Low": 20.0}[lod_name]

    # 计算每个LOD级别的屏幕度量值
    fov = 60.0
    screen_height = 1080

    lod_metrics = {}
    for lod_name in ["High", "Medium", "Low"]:
        geometric_error = _get_geometric_error(lod_name)
        screen_pixels, screen_ratio = calculate_screen_metric(geometric_error, distance, fov, screen_height)
        lod_metrics[lod_name] = {
            'geometric_error': geometric_error,
            'screen_pixels': screen_pixels,
            'screen_ratio': screen_ratio,
            'threshold': _get_screen_threshold(lod_name)
        }

    # 获取屏幕误差阈值
    thresholds = {lod: lod_metrics[lod]['threshold'] for lod in ["High", "Medium", "Low"]}

    # 使用High LOD的屏幕度量值来确定LOD级别
    high_metrics = lod_metrics["High"]
    target_lod = determine_lod_by_screen_metric(
        high_metrics['screen_pixels'],
        high_metrics['screen_ratio'],
        thresholds
    )

    # 距离回退机制（保持向后兼容）
    # if config:
    #     d_high = float(getattr(config, 'distance_threshold_high', 50.0))
    #     d_med = float(getattr(config, 'distance_threshold_medium', 100.0))
    # else:
    #     d_high, d_med = 50.0, 100.0

    # # 距离等级判断
    # if distance <= d_high:
    #     distance_lod = "High"
    # elif distance <= d_med:
    #     distance_lod = "Medium"
    # else:
    #     distance_lod = "Low"

    # 取更高质量的LOD（距离和屏幕误差的最大值）
    lod_priority = {"Low": 0, "Medium": 1, "High": 2}
    # final_lod_priority = max(lod_priority[target_lod], lod_priority[distance_lod])
    final_lod_priority = lod_priority[target_lod]
    final_lod = ["Low", "Medium", "High"][final_lod_priority]

    if verbose:
        print(f"Camera position: {camera_position}")
        print(f"Distance to region center: {distance:.1f}")
        print(f"LOD metrics:")
        for lod_name in ["High", "Medium", "Low"]:
            metrics = lod_metrics[lod_name]
            threshold = metrics['threshold']
            unit = "ratio" if threshold <= 1.0 else "px"
            print(f"  {lod_name}: geo_error={metrics['geometric_error']:.1f}m, "
                  f"screen={metrics['screen_pixels']:.1f}px ({metrics['screen_ratio']:.4f}ratio), "
                  f"threshold={threshold}{unit}")
        print(f"Screen-based LOD: {target_lod}")
        # print(f"Distance-based LOD: {distance_lod}")
        print(f"Final LOD level: {final_lod}")

    # 更新所有LOD级别的可见性
    lod_levels = ["High", "Medium", "Low"]
    for lod_name in lod_levels:
        lod_path = f"/World/SingleRegion/LOD_{lod_name}"
        lod_prim = stage.GetPrimAtPath(lod_path)

        if lod_prim:
            # 使用UsdGeom.Imageable设置可见性
            imageable = UsdGeom.Imageable(lod_prim)
            vis_attr = imageable.GetVisibilityAttr()

            # 设置可见性：只有目标LOD可见，其他都隐藏
            visible = (lod_name == final_lod)
            vis_attr.Set(UsdGeom.Tokens.inherited if visible else UsdGeom.Tokens.invisible)

            if verbose:
                print(f"  LOD_{lod_name}: {'Visible' if visible else 'Hidden'}")
        else:
            if verbose:
                print(f"  LOD_{lod_name}: Not found")

    # 返回用于日志/状态的结果
    return final_lod, distance, lod_metrics["High"]["screen_pixels"]

def _start_mainthread_update_subscription(update_interval: float = 1.0):
    """使用 Kit 的 update 事件在主线程上进行节流更新，替代线程定时器"""
    global realtime_update_active, realtime_update_data, current_lod_state, auto_update_subscription

    # 防止重复订阅
    if auto_update_subscription:
        try:
            auto_update_subscription.unsubscribe()
        except Exception:
            pass
        auto_update_subscription = None

    import omni.kit.app
    app = omni.kit.app.get_app()
    stream = app.get_update_event_stream()

    last_ts = time.time()
    accumulator = 0.0

    def on_update(e):
        nonlocal last_ts, accumulator
        if not realtime_update_active or not realtime_update_data:
            return

        now = time.time()
        dt = now - last_ts
        last_ts = now
        accumulator += dt

        if accumulator < update_interval:
            return
        accumulator = 0.0

        try:
            stage = realtime_update_data['stage']
            region_bounds = realtime_update_data['region_bounds']
            usdz_paths = realtime_update_data['usdz_paths']

            # 更新LOD可见性（静默），支持从全局或默认配置读取阈值
            # 尝试复用在 standalone 配置阶段创建的 config
            cfg = None
            try:
                cfg = realtime_update_data.get('config', None)
            except Exception:
                cfg = None
            # 更新LOD并获取指标（只调用一次）
            try:
                current_lod, distance, screen_error = update_lod_visibility_by_geometric_error(
                    stage, region_bounds, usdz_paths, verbose=False, config=cfg
                )
                if (current_lod_state["last_lod"] != current_lod or
                    abs((current_lod_state["last_distance"] or 0) - distance) > 10.0):
                    print(f"🔄 LOD switched to {current_lod} (distance: {distance:.1f}, screen: {screen_error:.2f})")
                    current_lod_state["last_lod"] = current_lod
                    current_lod_state["last_distance"] = distance
            except Exception:
                pass
        except Exception as ex:
            print(f"Error in main-thread update: {ex}")

    auto_update_subscription = stream.create_subscription_to_pop(on_update, name="LODUpdateOnMainThread")

def _async_update_loop():
    """异步更新循环（改为同步版本以避免协程警告）"""
    global realtime_update_active, realtime_update_data, current_lod_state
    
    def update_loop():
        last_update_time = 0
        update_interval = 0.5  # 增加更新间隔到0.5秒，减少性能压力
        
        while realtime_update_active and realtime_update_data:
            try:
                current_time = time.time()
                
                # 只在间隔时间到达时才更新
                if current_time - last_update_time >= update_interval:
                    stage = realtime_update_data['stage']
                    region_bounds = realtime_update_data['region_bounds']
                    usdz_paths = realtime_update_data['usdz_paths']
                    
                    # 更新LOD可见性
                    update_lod_visibility_by_geometric_error(stage, region_bounds, usdz_paths, verbose=False)
                    
                    # 获取当前相机位置和LOD状态
                    camera = stage.GetPrimAtPath("/World/Camera")
                    if camera:
                        xformable = UsdGeom.Xformable(camera)
                        transform = xformable.GetLocalTransformation()
                        camera_position = Gf.Vec3f(transform.ExtractTranslation())
                        
                        center = region_bounds.center
                        distance = math.sqrt(
                            (camera_position[0] - center[0])**2 + 
                            (camera_position[1] - center[1])**2 + 
                            (camera_position[2] - center[2])**2
                        )
                        
                        # 确定当前LOD级别
                        region_size = region_bounds.size
                        max_dimension = max(region_size[0], region_size[1], region_size[2])
                        
                        high_threshold = max_dimension * 0.5
                        medium_threshold = max_dimension * 1.0
                        
                        if distance <= high_threshold:
                            current_lod = "High"
                        elif distance <= medium_threshold:
                            current_lod = "Medium"
                        else:
                            current_lod = "Low"
                        
                        # 只在LOD状态改变时输出信息
                        if (current_lod_state["last_lod"] != current_lod or 
                            abs(current_lod_state["last_distance"] - distance) > 10.0):
                            
                            print(f"🔄 LOD switched to {current_lod} (distance: {distance:.1f})")
                            current_lod_state["last_lod"] = current_lod
                            current_lod_state["last_distance"] = distance
                    
                    last_update_time = current_time
                        
            except Exception as e:
                print(f"Error in async update loop: {e}")
            
            # 等待一小段时间，减少CPU占用
            time.sleep(0.2)
    
    # 启动更新循环
    import threading
    update_thread = threading.Thread(target=update_loop, daemon=True)
    update_thread.start()
    return update_thread

def start_automatic_lod_switching():
    """启动真正的自动LOD切换，使用Omniverse事件系统"""
    global realtime_update_active, realtime_update_data, auto_update_subscription
    
    print("\n=== Starting Automatic LOD Switching ===")
    
    # 配置USDZ文件路径（请根据实际情况修改）
    usdz_paths = {
        "High": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/20.usdz",
        "Medium": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/18.usdz", 
        "Low": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/17.usdz"
    }
    
    # 创建场景
    stage, region_bounds = create_single_region_scene_with_usdz(usdz_paths, config)
    
    # 保存数据到全局变量
    realtime_update_data = {
        'stage': stage,
        'region_bounds': region_bounds,
        'usdz_paths': usdz_paths
    }
    realtime_update_active = True
    
    # 方法1：使用主线程事件订阅（推荐，线程安全）
    try:
        print("Starting main-thread event-based automatic updates...")
        _start_mainthread_update_subscription(update_interval=1.0)
        print("✅ Automatic LOD switching started using main-thread event subscription!")
        print("The system will automatically update LOD every 1.0 seconds on the main thread.")
        return stage, region_bounds, None
    except Exception as e:
        print(f"Main-thread event subscription failed: {e}")
    
    # 方法2：使用线程循环（备选）
    try:
        print("Starting thread-based automatic updates...")
        # 启动线程更新循环
        update_thread = _async_update_loop()
        print("✅ Automatic LOD switching started using thread-based updates!")
        print("The system will automatically update LOD continuously.")
        return stage, region_bounds, None
    except Exception as e:
        print(f"Thread-based updates failed: {e}")
    
    # 如果所有方法都失败，回退到手动模式
    print("⚠️ All automatic methods failed, falling back to manual mode")
    print("You can use manual_update() to update LOD manually")
    return stage, region_bounds, None

def stop_automatic_lod_switching():
    """停止自动LOD切换"""
    global realtime_update_active, realtime_update_data, auto_update_subscription
    
    print("\n=== Stopping Automatic LOD Switching ===")
    
    # 停止自动更新
    realtime_update_active = False
    realtime_update_data = None
    
    # 取消订阅
    if auto_update_subscription:
        try:
            auto_update_subscription.unsubscribe()
        except Exception:
            pass
        auto_update_subscription = None
    print("✅ Automatic LOD switching stopped successfully!")

def check_current_lod_status():
    """检查当前LOD状态"""
    print("\n=== Current LOD Status ===")
    
    try:
        stage = omni.usd.get_context().get_stage()
        region_bounds, _ = get_nurec_bounds_from_stage()
        
        # 获取相机位置
        camera = stage.GetPrimAtPath("/World/Camera")
        if not camera:
            print("Camera not found!")
            return
        
        xformable = UsdGeom.Xformable(camera)
        transform = xformable.GetLocalTransformation()
        camera_position = Gf.Vec3f(transform.ExtractTranslation())
        
        # 计算距离
        center = region_bounds.center
        distance = math.sqrt(
            (camera_position[0] - center[0])**2 + 
            (camera_position[1] - center[1])**2 + 
            (camera_position[2] - center[2])**2
        )
        
        # 确定当前LOD级别
        region_size = region_bounds.size
        max_dimension = max(region_size[0], region_size[1], region_size[2])
        
        high_threshold = max_dimension * 0.5
        medium_threshold = max_dimension * 1.0
        
        if distance <= high_threshold:
            current_lod = "High"
        elif distance <= medium_threshold:
            current_lod = "Medium"
        else:
            current_lod = "Low"
        
        print(f"Camera position: {camera_position}")
        print(f"Distance to region center: {distance:.1f}")
        print(f"Current LOD level: {current_lod}")
        print(f"Distance thresholds:")
        print(f"  - High: 0-{high_threshold:.1f}")
        print(f"  - Medium: {high_threshold:.1f}-{medium_threshold:.1f}")
        print(f"  - Low: {medium_threshold:.1f}+")
        
        # 检查各LOD级别的可见性
        print(f"\nLOD visibility status:")
        lod_levels = ["High", "Medium", "Low"]
        for lod_name in lod_levels:
            lod_path = f"/World/SingleRegion/LOD_{lod_name}"
            lod_prim = stage.GetPrimAtPath(lod_path)
            
            if lod_prim:
                imageable = UsdGeom.Imageable(lod_prim)
                vis_attr = imageable.GetVisibilityAttr()
                visibility = vis_attr.Get()
                print(f"  - LOD_{lod_name}: {'Visible' if visibility == UsdGeom.Tokens.inherited else 'Hidden'}")
            else:
                print(f"  - LOD_{lod_name}: Not found")
                
    except Exception as e:
        print(f"Error checking LOD status: {e}")

def manual_lod_update():
    """手动更新LOD（用于测试）"""
    global realtime_update_active, realtime_update_data
    
    print("\n=== Manual LOD Update ===")
    
    # 如果实时更新处于活动状态，使用实时更新数据
    if realtime_update_active and realtime_update_data:
        stage = realtime_update_data['stage']
        region_bounds = realtime_update_data['region_bounds']
        usdz_paths = realtime_update_data['usdz_paths']
        
        # 执行一次LOD更新
        update_lod_visibility_by_geometric_error(stage, region_bounds, usdz_paths, verbose=False)
        
        # 获取当前相机位置和LOD状态用于状态跟踪
        camera = stage.GetPrimAtPath("/World/Camera")
        if camera:
            xformable = UsdGeom.Xformable(camera)
            transform = xformable.GetLocalTransformation()
            camera_position = Gf.Vec3f(transform.ExtractTranslation())
            
            center = region_bounds.center
            distance = math.sqrt(
                (camera_position[0] - center[0])**2 + 
                (camera_position[1] - center[1])**2 + 
                (camera_position[2] - center[2])**2
            )
            
            # 确定当前LOD级别
            region_size = region_bounds.size
            max_dimension = max(region_size[0], region_size[1], region_size[2])
            
            high_threshold = max_dimension * 0.5
            medium_threshold = max_dimension * 1.0
            
            if distance <= high_threshold:
                current_lod = "High"
            elif distance <= medium_threshold:
                current_lod = "Medium"
            else:
                current_lod = "Low"
            
            # 只在LOD状态改变时输出信息
            global current_lod_state
            if (current_lod_state["last_lod"] != current_lod or 
                abs(current_lod_state["last_distance"] - distance) > 10.0):
                
                print(f"LOD switched to {current_lod} (distance: {distance:.1f})")
                current_lod_state["last_lod"] = current_lod
                current_lod_state["last_distance"] = distance
        
        print("Manual LOD update completed!")
    else:
        # 配置USDZ文件路径
        usdz_paths = {
            "High": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/20.usdz",
            "Medium": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/18.usdz", 
            "Low": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/17.usdz"
        }
        
        # 获取当前stage和区域边界
        stage = omni.usd.get_context().get_stage()
        region_bounds, _ = get_nurec_bounds_from_stage()
        
        # 执行一次LOD更新
        update_lod_visibility_by_geometric_error(stage, region_bounds, usdz_paths)
        
        print("Manual LOD update completed!")

# ============================================================================
# 主执行函数 - Standalone版本
# ============================================================================

def run_standalone_mode(config):
    """运行standalone模式"""
    print("=== Simple LOD Example Standalone Mode ===")
    
    # 1. 检查USD文件路径
    if not config.usd_file_path or config.usd_file_path == "C:/test-usd-path":
        print("ERROR: Please set USD file path in config.usd_file_path")
        return None
    
    # 2. 加载USD文件
    if not load_usd_stage(config.usd_file_path):
        return None
    
    # 3. 等待渲染系统初始化
    if not wait_for_render_initialization():
        print("WARNING: Render initialization may not be complete")
    
    # 4. 自动启动运行时（如果配置启用）
    if config.auto_start_runtime:
        if not start_runtime():
            print("WARNING: Failed to start runtime, continuing...")
    
    # 5. 启动自动LOD切换
    print(f"\nStarting Automatic LOD Switching...")
    stage, region_bounds, scheduler = start_automatic_lod_switching_with_config(config)
    
    print(f"\n=== Standalone mode setup completed ===")
    print(f"USD file: {config.usd_file_path}")
    print(f"Camera path: {config.camera_path}")
    print(f"Auto mode: {config.auto_mode}")
    print(f"Distance thresholds - High: {config.distance_threshold_high}m, Medium: {config.distance_threshold_medium}m")
    print(f"Timer interval: {config.timer_interval}s")
    print(f"Debug info: {config.debug_info}")
    
    return stage, region_bounds, scheduler

def start_automatic_lod_switching_with_config(config):
    """使用配置启动自动LOD切换"""
    global realtime_update_active, realtime_update_data, auto_update_subscription
    
    print("\n=== Starting Automatic LOD Switching with Config ===")
    
    # 创建场景
    stage, region_bounds = create_single_region_scene_with_usdz(config.usdz_paths, config)
    
    # 保存数据到全局变量
    realtime_update_data = {
        'stage': stage,
        'region_bounds': region_bounds,
        'usdz_paths': config.usdz_paths,
        'config': config
    }
    realtime_update_active = True
    
    # 方法1：使用主线程事件订阅（推荐，线程安全）
    try:
        print("Starting main-thread event-based automatic updates...")
        _start_mainthread_update_subscription(update_interval=config.timer_interval if hasattr(config, 'timer_interval') else 1.0)
        print("✅ Automatic LOD switching started using main-thread event subscription!")
        print(f"The system will automatically update LOD every {config.timer_interval if hasattr(config, 'timer_interval') else 1.0} seconds on the main thread.")
        return stage, region_bounds, None
    except Exception as e:
        print(f"Main-thread event subscription failed: {e}")
    
    # 方法2：使用线程循环（备选）
    try:
        print("Starting thread-based automatic updates...")
        # 启动线程更新循环
        update_thread = _async_update_loop()
        print("✅ Automatic LOD switching started using thread-based updates!")
        print("The system will automatically update LOD continuously.")
        return stage, region_bounds, None
    except Exception as e:
        print(f"Thread-based updates failed: {e}")
    
    # 如果所有方法都失败，回退到手动模式
    print("⚠️ All automatic methods failed, falling back to manual mode")
    print("You can use manual_update() to update LOD manually")
    return stage, region_bounds, None

def main():
    """主函数 - Standalone版本，直接运行自动LOD切换"""
    print("Simple LOD Example - Standalone Version")
    print("=" * 60)
    print("This version runs automatically without user interaction.")
    print("The system will start automatic LOD switching immediately.")
    print("=" * 60)
    
    try:
        # 直接启动自动LOD切换
        print("\n🚀 Starting automatic LOD switching...")
        stage, region_bounds, scheduler = start_automatic_lod_switching()
        
        print("\n" + "=" * 50)
        print("✅ Automatic LOD switching started successfully!")
        print("\nFeatures:")
        print("- True automatic LOD switching based on camera distance")
        print("- Multiple fallback methods for maximum compatibility")
        print("- Real-time updates as you move the camera")
        print("- Coordinate system correction applied")
        print("- Uses actual USDZ files with visibility control")
        print("\nThe system is now running automatically!")
        print("Move the camera in Omniverse to see LOD changes.")
        print("=" * 50)
        
        # 显示初始状态
        print("\n📊 Initial LOD Status:")
        check_current_lod_status()
        
        print("\n🎯 System is ready! Move the camera to test LOD switching.")
        print("You can call the following functions from the console:")
        print("  - check_current_lod_status()  # Check current status")
        print("  - manual_lod_update()         # Manual update")
        print("  - stop_automatic_lod_switching()  # Stop the system")
        
    except Exception as e:
        print(f"❌ Error starting automatic LOD switching: {e}")
        import traceback
        traceback.print_exc()

# 便捷函数 - 用户可以直接调用这些函数
def start_lod():
    """便捷函数：启动自动LOD切换"""
    return start_automatic_lod_switching()

def start_auto_lod():
    """便捷函数：启动自动LOD切换"""
    return start_automatic_lod_switching()

def stop_lod():
    """便捷函数：停止自动LOD切换"""
    return stop_automatic_lod_switching()

def check_status():
    """便捷函数：检查当前LOD状态"""
    return check_current_lod_status()

def manual_update():
    """便捷函数：手动更新LOD"""
    return manual_lod_update()

def start_standalone_lod(usd_file_path, usdz_paths=None):
    """便捷函数：启动standalone模式的LOD切换"""
    config = StandaloneConfig()
    config.usd_file_path = usd_file_path
    
    if usdz_paths:
        config.usdz_paths = usdz_paths
    
    return run_standalone_mode(config)

def quick_start():
    """快速启动函数 - 使用默认配置"""
    config = StandaloneConfig()
    
    # 使用默认配置
    config.usd_file_path = "E:/wanleqi/LOD_Demo/lod-demo-block-0000.usd"
    config.usdz_paths = {
        "High": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/20.usdz",
        "Medium": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/18.usdz", 
        "Low": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/17.usdz"
    }
    
    return run_standalone_mode(config)

# 使用说明
print("\n" + "=" * 60)
print("Simple LOD Example - Standalone Version")
print("=" * 60)
print("\nThis script supports both Isaac Sim standalone mode and Omniverse mode.")
print("No user interaction required!")
print("\n=== Isaac Sim Standalone Mode ===")
print("Available functions:")
print("  - quick_start()                    - Quick start with default config")
print("  - start_standalone_lod(usd_path)   - Start with custom USD file")
print("  - run_standalone_mode(config)      - Start with full config")
print("\n=== Omniverse Mode ===")
print("Available functions:")
print("  - start_lod()      - Start automatic LOD switching")
print("  - stop_lod()       - Stop automatic LOD switching")
print("  - check_status()   - Check current LOD status")
print("  - manual_update()  - Manual LOD update")
print("\nAutomatic Update Methods:")
print("  - Timer-based: Updates every 0.5 seconds (most stable)")
print("  - Thread-based: Continuous updates (backup)")
print("\nThe system will automatically try multiple methods")
print("to ensure LOD switching works in your environment.")
print("=" * 60)

# 示例使用
if __name__ == "__main__":
    print("=== Simple LOD Example Standalone Demo ===")

    # 导入必要的模块
    import omni.kit.commands
    import omni.kit.app
    import omni.usd
    import omni.timeline
    from pxr import Usd, UsdGeom, Gf
    
    try:
        # 创建standalone配置
        config = StandaloneConfig()
        
        # ===== 用户配置区域 =====
        # 请修改以下配置项：
        
        # 1. 设置USD文件路径（必需）
        config.usd_file_path = "E:/wanleqi/LOD_Demo/lod-demo-block-0000.usd"
        
        # 2. 设置USDZ文件路径（必需）
        config.usdz_paths = {
            "High": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/20.usdz",
            "Medium": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/18.usdz", 
            "Low": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/17.usdz"
        }
        
        # 3. 相机配置（可选）
        config.camera_path = "/World/Camera"  # 相机prim路径
        
        # 4. LOD切换配置（可选）
        config.distance_threshold_high = 50.0  # 高LOD距离阈值（米）
        config.distance_threshold_medium = 100.0  # 中LOD距离阈值（米）
        config.auto_mode = "movement"  # "movement" 或 "timer"
        config.timer_interval = 0.1  # 定时器间隔（秒）
        config.debug_info = True  # 是否输出调试信息
        
        # 5. 运行时控制（可选）
        config.auto_start_runtime = True  # 是否自动开启运行时
        
        # 6. 相机移动模拟配置（可选）
        config.camera_start_position = Gf.Vec3f(0, 0, 50)  # 相机起始位置（更近，确保High LOD）
        config.camera_target_position = Gf.Vec3f(20, 0, 100)  # 相机目标位置（更远，测试LOD切换）
        config.camera_movement_duration = 30.0  # 移动持续时间（秒）
        config.camera_movement_loop = True  # 是否循环移动
        
        # ===== 配置结束 =====
        
        # 运行standalone模式
        if config.usd_file_path != "C:/test-usd-path":
            result = run_standalone_mode(config)
            
            if result:
                stage, region_bounds, scheduler = result
                
                # 保持脚本运行（可选）
                print("\nPress Ctrl+C to stop...")
                try:
                    print("Starting main loop...")
                    while True:
                        simulation_app.update()  # 使用 simulation_app.update() 而不是 time.sleep()
                        time.sleep(0.01)  # 短暂延迟以避免过度占用CPU
                except KeyboardInterrupt:
                    print("\nStopping...")
                    if scheduler:
                        stop_automatic_lod_switching()
            else:
                print("\nFailed to start standalone mode")
        else:
            print("\nERROR: Please set config.usd_file_path to your actual USD file path")
            print("Example:")
            print("config.usd_file_path = 'C:/Users/<USER>/Documents/scene.usd'")
            print("or")
            print("config.usd_file_path = '/home/<USER>/scene.usd'")
    
    except Exception as e:
        print(f"\nERROR: An error occurred: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理 Isaac Sim SimulationApp
        if simulation_app is not None:
            print("\nCleaning up Isaac Sim SimulationApp...")
            simulation_app.close()
            print("✓ Isaac Sim SimulationApp closed")
        
        print("\n=== Script execution completed ===") 