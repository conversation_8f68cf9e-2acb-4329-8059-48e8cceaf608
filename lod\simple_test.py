import math

def calculate_screen_error(geometric_error, distance):
    """计算屏幕误差"""
    fov = 60.0
    screen_height = 1080
    focal_len_px = (screen_height / 2) / math.tan(math.radians(fov / 2))
    screen_pixels = (geometric_error / distance) * focal_len_px
    return screen_pixels

print("=== 距离与屏幕误差关系 ===")
print("几何误差: 0.5米")
print()

geometric_error = 0.5
distances = [25, 50, 100, 200, 500]

for distance in distances:
    screen_error = calculate_screen_error(geometric_error, distance)
    print(f"距离 {distance:3d}m → 屏幕误差 {screen_error:6.1f}px")

print()
print("✅ 结论: 距离越近，屏幕误差越大")
print("这是正确的！同样的几何误差在近距离时在屏幕上显示得更大")

print()
print("=== LOD逻辑 ===")
print("阈值: High≥2px, Medium≥8px, Low≥20px")
print()

thresholds = {"High": 2.0, "Medium": 8.0, "Low": 20.0}

for distance in distances:
    screen_error = calculate_screen_error(geometric_error, distance)
    
    if screen_error >= thresholds["High"]:
        lod = "High"
    elif screen_error >= thresholds["Medium"]:
        lod = "Medium"  
    elif screen_error >= thresholds["Low"]:
        lod = "Low"
    else:
        lod = "Very Low"
    
    print(f"距离 {distance:3d}m → 误差 {screen_error:6.1f}px → {lod} LOD")

print()
print("✅ 逻辑: 屏幕误差大时用高质量LOD，误差小时用低质量LOD")
