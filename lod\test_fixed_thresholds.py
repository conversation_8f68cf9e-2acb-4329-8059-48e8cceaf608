"""
测试修复后的阈值逻辑
验证Medium LOD现在可以正确触发
"""

import math

def calculate_screen_metric(geometric_error, distance, fov=60.0, screen_height=1080):
    """计算几何误差投影到屏幕的度量值"""
    if distance <= 0:
        return float('inf'), float('inf')
    
    # 计算焦距（像素单位）
    focal_len_px = (screen_height / 2) / math.tan(math.radians(fov / 2))
    
    # 几何误差投影到屏幕的像素误差
    screen_pixels = (geometric_error / distance) * focal_len_px
    
    # 计算屏幕比例
    screen_ratio = screen_pixels / screen_height
    
    return screen_pixels, screen_ratio

def determine_lod_by_screen_metric(screen_pixels, screen_ratio, thresholds):
    """根据屏幕度量值确定LOD级别"""
    # 自动判断阈值单位（<=1 则按比例，否则按像素）
    def _get_metric_value(threshold_value):
        if threshold_value <= 1.0:
            return screen_ratio
        else:
            return screen_pixels
    
    # 获取各级别阈值
    high_threshold = thresholds.get("High", 20.0)
    medium_threshold = thresholds.get("Medium", 8.0)
    low_threshold = thresholds.get("Low", 2.0)
    
    # 获取对应的度量值（使用High LOD的单位类型）
    metric_value = _get_metric_value(high_threshold)
    
    # 正确的LOD判断逻辑：屏幕误差大于等于阈值时使用该LOD
    # 屏幕误差大 → 需要高质量LOD
    if metric_value >= high_threshold:
        return "High"
    elif metric_value >= medium_threshold:
        return "Medium"
    elif metric_value >= low_threshold:
        return "Low"
    else:
        # 屏幕误差很小，使用最低质量即可
        return "Low"

def test_fixed_thresholds():
    """测试修复后的阈值逻辑"""
    print("=== 修复后的阈值逻辑测试 ===\n")
    
    # 修复后的阈值配置
    thresholds = {
        "High": 20.0,   # 20像素 - 误差很大时用高质量
        "Medium": 8.0,  # 8像素 - 误差中等时用中质量
        "Low": 2.0      # 2像素 - 误差较小时用低质量
    }
    
    print("修复后的阈值配置:")
    print(f"High LOD: >= {thresholds['High']}px (误差很大时用高质量)")
    print(f"Medium LOD: >= {thresholds['Medium']}px (误差中等时用中质量)")
    print(f"Low LOD: >= {thresholds['Low']}px (误差较小时用低质量)")
    print(f"Very Low: < {thresholds['Low']}px (误差很小时用最低质量)")
    
    print(f"\n{'距离(m)':<8} {'屏幕误差(px)':<14} {'LOD级别':<10} {'区间说明':<20}")
    print(f"{'-'*60}")
    
    geometric_error = 0.5  # High LOD的几何误差
    test_distances = [10, 15, 20, 25, 30, 50, 75, 100, 150, 200, 300, 500, 1000]
    
    for distance in test_distances:
        screen_pixels, screen_ratio = calculate_screen_metric(geometric_error, distance)
        lod = determine_lod_by_screen_metric(screen_pixels, screen_ratio, thresholds)
        
        # 区间说明
        if screen_pixels >= 20.0:
            interval = "≥20px → High"
        elif screen_pixels >= 8.0:
            interval = "8-19px → Medium"
        elif screen_pixels >= 2.0:
            interval = "2-7px → Low"
        else:
            interval = "<2px → Very Low"
        
        print(f"{distance:<8} {screen_pixels:>12.1f} {lod:<10} {interval}")

def test_edge_cases():
    """测试边界情况"""
    print(f"\n\n=== 边界情况测试 ===\n")
    
    thresholds = {"High": 20.0, "Medium": 8.0, "Low": 2.0}
    
    # 测试精确的阈值边界
    test_cases = [
        (25.0, "应该是High"),
        (20.0, "应该是High"),
        (19.9, "应该是Medium"),
        (10.0, "应该是Medium"),
        (8.0, "应该是Medium"),
        (7.9, "应该是Low"),
        (5.0, "应该是Low"),
        (2.0, "应该是Low"),
        (1.9, "应该是Very Low"),
        (1.0, "应该是Very Low"),
    ]
    
    print(f"{'屏幕误差(px)':<14} {'LOD级别':<10} {'预期':<15} {'状态':<6}")
    print(f"{'-'*50}")
    
    for screen_error, expected_desc in test_cases:
        screen_ratio = screen_error / 1080
        lod = determine_lod_by_screen_metric(screen_error, screen_ratio, thresholds)
        
        # 判断预期
        if "High" in expected_desc:
            expected = "High"
        elif "Medium" in expected_desc:
            expected = "Medium"
        elif "Very Low" in expected_desc:
            expected = "Low"  # Very Low也返回Low
        else:
            expected = "Low"
        
        status = "✅" if lod == expected else "❌"
        print(f"{screen_error:<14} {lod:<10} {expected_desc:<15} {status}")

def test_medium_lod_coverage():
    """专门测试Medium LOD的覆盖范围"""
    print(f"\n\n=== Medium LOD覆盖范围测试 ===\n")
    
    thresholds = {"High": 20.0, "Medium": 8.0, "Low": 2.0}
    
    print("测试Medium LOD是否能正确触发:")
    print(f"Medium LOD区间: 8.0px <= 屏幕误差 < 20.0px")
    
    # 测试Medium区间内的多个点
    test_errors = [8.0, 9.0, 10.0, 12.0, 15.0, 18.0, 19.9]
    
    medium_count = 0
    for error in test_errors:
        screen_ratio = error / 1080
        lod = determine_lod_by_screen_metric(error, screen_ratio, thresholds)
        if lod == "Medium":
            medium_count += 1
        print(f"屏幕误差 {error:4.1f}px → {lod} LOD")
    
    print(f"\n✅ Medium LOD触发次数: {medium_count}/{len(test_errors)}")
    if medium_count > 0:
        print("🎉 修复成功！Medium LOD现在可以正确触发了！")
    else:
        print("❌ 仍有问题，Medium LOD没有触发")

if __name__ == "__main__":
    test_fixed_thresholds()
    test_edge_cases()
    test_medium_lod_coverage()
    
    print(f"\n\n=== 修复总结 ===")
    print("✅ 问题修复:")
    print("  - 调整阈值数值: High=20px, Medium=8px, Low=2px")
    print("  - 现在有明确的区间划分:")
    print("    * ≥20px → High LOD")
    print("    * 8-19px → Medium LOD") 
    print("    * 2-7px → Low LOD")
    print("    * <2px → Very Low LOD")
    
    print(f"\n✅ Medium LOD现在有了自己的区间！")
    print("  - 修复前: 只有High(≥2px)和Low(<2px)")
    print("  - 修复后: High(≥20px), Medium(8-19px), Low(2-7px), Very Low(<2px)")
