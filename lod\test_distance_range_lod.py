"""
测试基于距离范围的LOD选择
验证新的距离范围映射逻辑
"""

import math

def calculate_sse(geometric_error, distance_to_camera, screen_width=1920, h_fov=60.0):
    """计算屏幕空间误差(SSE)"""
    if distance_to_camera <= 0:
        return float('inf')
    
    sse = (geometric_error * screen_width) / (2 * distance_to_camera * math.tan(math.radians(h_fov / 2)))
    return sse

def calculate_lod_distance_ranges(lod_configs, maximum_screen_space_error=8.0, screen_width=1920, h_fov=60.0):
    """计算每个LOD对应的距离范围"""
    tan_half_fov = math.tan(math.radians(h_fov / 2))
    
    lod_max_distances = {}
    for lod_name, geometric_error in lod_configs.items():
        max_distance = (geometric_error * screen_width) / (2 * maximum_screen_space_error * tan_half_fov)
        lod_max_distances[lod_name] = max_distance
    
    # 按距离排序LOD（距离越小，质量越高）
    sorted_lods = sorted(lod_max_distances.items(), key=lambda x: x[1])
    
    # 构建距离范围
    distance_ranges = {}
    prev_distance = 0
    
    for i, (lod_name, max_distance) in enumerate(sorted_lods):
        if i == len(sorted_lods) - 1:
            # 最后一个LOD，距离范围到无穷大
            distance_ranges[lod_name] = (prev_distance, float('inf'))
        else:
            distance_ranges[lod_name] = (prev_distance, max_distance)
        prev_distance = max_distance
    
    return distance_ranges, lod_max_distances

def select_lod_by_distance_range(lod_configs, distance_to_camera, maximum_screen_space_error=8.0, screen_width=1920, h_fov=60.0, verbose=False):
    """基于距离范围选择LOD"""
    # 计算距离范围
    distance_ranges, lod_max_distances = calculate_lod_distance_ranges(
        lod_configs, maximum_screen_space_error, screen_width, h_fov
    )
    
    if verbose:
        print(f"距离范围映射 (SSE阈值={maximum_screen_space_error}px):")
        for lod_name in ["High", "Medium", "Low"]:
            if lod_name in distance_ranges:
                min_dist, max_dist = distance_ranges[lod_name]
                max_dist_str = f"{max_dist:.1f}m" if max_dist != float('inf') else "∞"
                print(f"  {lod_name}: {min_dist:.1f}m - {max_dist_str}")
        print(f"当前距离: {distance_to_camera:.1f}m")
    
    # 根据距离选择LOD
    selected_lod = "Low"  # 默认
    for lod_name, (min_dist, max_dist) in distance_ranges.items():
        if min_dist <= distance_to_camera < max_dist:
            selected_lod = lod_name
            break
    
    # 计算各LOD的SSE信息（用于显示）
    lod_sse_info = {}
    for lod_name, geometric_error in lod_configs.items():
        sse = calculate_sse(geometric_error, distance_to_camera, screen_width, h_fov)
        lod_sse_info[lod_name] = {
            'geometric_error': geometric_error,
            'sse': sse,
            'max_distance': lod_max_distances.get(lod_name, 0),
            'distance_range': distance_ranges.get(lod_name, (0, 0))
        }
    
    return selected_lod, lod_sse_info

def test_distance_range_calculation():
    """测试距离范围计算"""
    print("=== 距离范围计算测试 ===\n")
    
    config = {"High": 0.5, "Medium": 2.0, "Low": 4.0}
    sse_threshold = 8.0
    
    distance_ranges, max_distances = calculate_lod_distance_ranges(config, sse_threshold)
    
    print(f"配置: {config}")
    print(f"SSE阈值: {sse_threshold}px")
    
    print(f"\n各LOD的最大距离:")
    for lod_name in ["High", "Medium", "Low"]:
        if lod_name in max_distances:
            print(f"  {lod_name}: {max_distances[lod_name]:.1f}m")
    
    print(f"\n距离范围分配:")
    for lod_name in ["High", "Medium", "Low"]:
        if lod_name in distance_ranges:
            min_dist, max_dist = distance_ranges[lod_name]
            max_dist_str = f"{max_dist:.1f}m" if max_dist != float('inf') else "∞"
            print(f"  {lod_name}: {min_dist:.1f}m - {max_dist_str}")

def test_lod_selection_by_distance():
    """测试基于距离的LOD选择"""
    print(f"\n\n=== 基于距离的LOD选择测试 ===\n")
    
    config = {"High": 0.5, "Medium": 2.0, "Low": 4.0}
    sse_threshold = 8.0
    
    # 测试不同距离
    test_distances = [50, 100, 150, 200, 300, 500, 800, 1000, 1500, 2000]
    
    print(f"配置: {config}")
    print(f"SSE阈值: {sse_threshold}px")
    
    print(f"\n{'距离(m)':<8} {'选择LOD':<8} {'High SSE':<10} {'Medium SSE':<12} {'Low SSE':<10} {'说明':<20}")
    print(f"{'-'*75}")
    
    for distance in test_distances:
        selected_lod, lod_info = select_lod_by_distance_range(config, distance, sse_threshold)
        
        high_sse = lod_info["High"]["sse"]
        medium_sse = lod_info["Medium"]["sse"]
        low_sse = lod_info["Low"]["sse"]
        
        # 获取距离范围信息
        selected_range = lod_info[selected_lod]["distance_range"]
        min_dist, max_dist = selected_range
        max_dist_str = f"{max_dist:.0f}" if max_dist != float('inf') else "∞"
        range_str = f"{min_dist:.0f}-{max_dist_str}m"
        
        print(f"{distance:<8} {selected_lod:<8} {high_sse:<10.1f} {medium_sse:<12.1f} {low_sse:<10.1f} {range_str}")

def test_different_sse_thresholds():
    """测试不同SSE阈值的影响"""
    print(f"\n\n=== 不同SSE阈值影响测试 ===\n")
    
    config = {"High": 0.5, "Medium": 2.0, "Low": 4.0}
    thresholds = [4.0, 8.0, 16.0, 32.0]
    test_distance = 200  # 固定距离
    
    print(f"配置: {config}")
    print(f"固定距离: {test_distance}m")
    
    print(f"\n{'SSE阈值':<8} {'High范围':<15} {'Medium范围':<15} {'Low范围':<15} {'选择LOD':<8}")
    print(f"{'-'*70}")
    
    for threshold in thresholds:
        distance_ranges, _ = calculate_lod_distance_ranges(config, threshold)
        selected_lod, _ = select_lod_by_distance_range(config, test_distance, threshold)
        
        # 格式化距离范围
        ranges_str = {}
        for lod_name in ["High", "Medium", "Low"]:
            if lod_name in distance_ranges:
                min_dist, max_dist = distance_ranges[lod_name]
                max_dist_str = f"{max_dist:.0f}" if max_dist != float('inf') else "∞"
                ranges_str[lod_name] = f"{min_dist:.0f}-{max_dist_str}"
            else:
                ranges_str[lod_name] = "N/A"
        
        print(f"{threshold:<8} {ranges_str['High']:<15} {ranges_str['Medium']:<15} {ranges_str['Low']:<15} {selected_lod:<8}")

def test_edge_cases():
    """测试边界情况"""
    print(f"\n\n=== 边界情况测试 ===\n")
    
    config = {"High": 0.5, "Medium": 2.0, "Low": 4.0}
    sse_threshold = 8.0
    
    # 获取距离范围
    distance_ranges, _ = calculate_lod_distance_ranges(config, sse_threshold)
    
    print("距离范围:")
    for lod_name in ["High", "Medium", "Low"]:
        if lod_name in distance_ranges:
            min_dist, max_dist = distance_ranges[lod_name]
            max_dist_str = f"{max_dist:.1f}m" if max_dist != float('inf') else "∞"
            print(f"  {lod_name}: {min_dist:.1f}m - {max_dist_str}")
    
    # 测试边界点
    boundary_tests = []
    for lod_name in ["High", "Medium", "Low"]:
        if lod_name in distance_ranges:
            min_dist, max_dist = distance_ranges[lod_name]
            if max_dist != float('inf'):
                # 测试边界点前后
                boundary_tests.extend([
                    (max_dist - 1, f"{lod_name}边界前"),
                    (max_dist, f"{lod_name}边界点"),
                    (max_dist + 1, f"{lod_name}边界后")
                ])
    
    print(f"\n边界点测试:")
    print(f"{'距离(m)':<8} {'选择LOD':<8} {'说明':<15}")
    print(f"{'-'*35}")
    
    for distance, description in boundary_tests:
        selected_lod, _ = select_lod_by_distance_range(config, distance, sse_threshold)
        print(f"{distance:<8.1f} {selected_lod:<8} {description}")

def compare_old_vs_new_logic():
    """对比旧逻辑和新逻辑"""
    print(f"\n\n=== 旧逻辑 vs 新逻辑对比 ===\n")
    
    config = {"High": 0.5, "Medium": 2.0, "Low": 4.0}
    sse_threshold = 8.0
    test_distances = [100, 200, 500, 1000, 2000]
    
    print(f"配置: {config}")
    print(f"SSE阈值: {sse_threshold}px")
    
    print(f"\n{'距离(m)':<8} {'旧逻辑':<8} {'新逻辑':<8} {'改进效果':<15}")
    print(f"{'-'*45}")
    
    for distance in test_distances:
        # 新逻辑：基于距离范围
        new_lod, _ = select_lod_by_distance_range(config, distance, sse_threshold)
        
        # 模拟旧逻辑：总是选择High（因为High SSE总是最小且满足阈值）
        high_sse = calculate_sse(config["High"], distance)
        old_lod = "High" if high_sse <= sse_threshold else "Low"
        
        # 分析改进效果
        if old_lod == new_lod:
            improvement = "无变化"
        elif old_lod == "High" and new_lod in ["Medium", "Low"]:
            improvement = "性能提升"
        else:
            improvement = "其他"
        
        print(f"{distance:<8} {old_lod:<8} {new_lod:<8} {improvement}")

def analyze_performance_impact():
    """分析性能影响"""
    print(f"\n\n=== 性能影响分析 ===\n")
    
    config = {"High": 0.5, "Medium": 2.0, "Low": 4.0}
    sse_threshold = 8.0
    
    # 统计不同距离范围的LOD分布
    distance_samples = list(range(50, 2001, 50))  # 50m到2000m，每50m一个样本
    lod_counts = {"High": 0, "Medium": 0, "Low": 0}
    
    for distance in distance_samples:
        selected_lod, _ = select_lod_by_distance_range(config, distance, sse_threshold)
        lod_counts[selected_lod] += 1
    
    total_samples = len(distance_samples)
    
    print("LOD分布统计 (距离50m-2000m):")
    for lod_name in ["High", "Medium", "Low"]:
        count = lod_counts[lod_name]
        percentage = (count / total_samples) * 100
        print(f"  {lod_name}: {count}次 ({percentage:.1f}%)")
    
    print(f"\n性能优化效果:")
    medium_low_percentage = (lod_counts["Medium"] + lod_counts["Low"]) / total_samples * 100
    print(f"  使用Medium/Low LOD的比例: {medium_low_percentage:.1f}%")
    print(f"  相比总是使用High LOD，性能提升显著")

if __name__ == "__main__":
    test_distance_range_calculation()
    test_lod_selection_by_distance()
    test_different_sse_thresholds()
    test_edge_cases()
    compare_old_vs_new_logic()
    analyze_performance_impact()
    
    print(f"\n{'='*60}")
    print("🎯 新逻辑优势:")
    print("1. 直观的距离范围映射，易于理解和调试")
    print("2. 避免了'总是选择High LOD'的问题")
    print("3. 根据实际距离合理分配LOD级别")
    print("4. 显著的性能优化效果")
    
    print(f"\n💡 关键改进:")
    print("- 距离<104m: High LOD (保证近距离质量)")
    print("- 距离104-416m: Medium LOD (平衡质量和性能)")
    print("- 距离>416m: Low LOD (优化远距离性能)")
    print("- 基于距离范围的选择逻辑更加可靠")
