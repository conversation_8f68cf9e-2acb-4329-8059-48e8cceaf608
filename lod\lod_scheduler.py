from pxr import Usd, UsdGeom, Gf
import omni.usd
import math
import json
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class LODLevel(Enum):
    """LOD级别枚举"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    VERY_LOW = "very_low"

@dataclass
class BoundingBox:
    """边界框数据结构"""
    min_point: Gf.Vec3f
    max_point: Gf.Vec3f
    
    @property
    def center(self) -> Gf.Vec3f:
        """计算边界框中心点"""
        return (self.min_point + self.max_point) * 0.5
    
    @property
    def size(self) -> Gf.Vec3f:
        """计算边界框尺寸"""
        return self.max_point - self.min_point
    
    @property
    def area(self) -> float:
        """计算边界框表面积"""
        size = self.size
        return 2 * (size[0] * size[1] + size[1] * size[2] + size[0] * size[2])
    
    @property
    def volume(self) -> float:
        """计算边界框体积"""
        size = self.size
        return size[0] * size[1] * size[2]
    
    def contains_point(self, point: Gf.Vec3f) -> bool:
        """检查点是否在边界框内"""
        return (self.min_point[0] <= point[0] <= self.max_point[0] and
                self.min_point[1] <= point[1] <= self.max_point[1] and
                self.min_point[2] <= point[2] <= self.max_point[2])
    
    def intersects(self, other: 'BoundingBox') -> bool:
        """检查两个边界框是否相交"""
        return not (self.max_point[0] < other.min_point[0] or
                   self.min_point[0] > other.max_point[0] or
                   self.max_point[1] < other.min_point[1] or
                   self.min_point[1] > other.max_point[1] or
                   self.max_point[2] < other.min_point[2] or
                   self.min_point[2] > other.max_point[2])

@dataclass
class LODTile:
    """LOD瓦片数据结构"""
    id: str
    bounding_box: BoundingBox
    lod_level: LODLevel
    usdz_path: str
    screen_error: float  # 屏幕误差阈值
    distance_threshold: float  # 距离阈值
    children: List['LODTile'] = None
    
    def __post_init__(self):
        if self.children is None:
            self.children = []

class OctreeNode:
    """八叉树节点"""
    def __init__(self, bounding_box: BoundingBox, max_depth: int = 8, min_area_threshold: float = 100.0):
        self.bounding_box = bounding_box
        self.max_depth = max_depth
        self.min_area_threshold = min_area_threshold
        self.children: List[Optional[OctreeNode]] = [None] * 8
        self.lod_tiles: List[LODTile] = []
        self.depth = 0
    
    def subdivide(self, current_depth: int = 0):
        """细分八叉树节点"""
        if current_depth >= self.max_depth or self.bounding_box.area <= self.min_area_threshold:
            return
        
        center = self.bounding_box.center
        min_point = self.bounding_box.min_point
        max_point = self.bounding_box.max_point
        
        # 创建8个子节点
        child_boxes = [
            BoundingBox(min_point, center),  # 000
            BoundingBox(Gf.Vec3f(center[0], min_point[1], min_point[2]), 
                       Gf.Vec3f(max_point[0], center[1], center[2])),  # 100
            BoundingBox(Gf.Vec3f(center[0], center[1], min_point[2]), 
                       Gf.Vec3f(max_point[0], max_point[1], center[2])),  # 110
            BoundingBox(Gf.Vec3f(min_point[0], center[1], min_point[2]), 
                       Gf.Vec3f(center[0], max_point[1], center[2])),  # 010
            BoundingBox(Gf.Vec3f(min_point[0], min_point[1], center[2]), 
                       Gf.Vec3f(center[0], center[1], max_point[2])),  # 001
            BoundingBox(Gf.Vec3f(center[0], min_point[1], center[2]), 
                       Gf.Vec3f(max_point[0], center[1], max_point[2])),  # 101
            BoundingBox(Gf.Vec3f(center[0], center[1], center[2]), max_point),  # 111
            BoundingBox(Gf.Vec3f(min_point[0], center[1], center[2]), 
                       Gf.Vec3f(center[0], max_point[1], max_point[2]))   # 011
        ]
        
        for i, child_box in enumerate(child_boxes):
            self.children[i] = OctreeNode(child_box, self.max_depth, self.min_area_threshold)
            self.children[i].depth = current_depth + 1
            self.children[i].subdivide(current_depth + 1)
    
    def add_lod_tile(self, lod_tile: LODTile):
        """添加LOD瓦片到节点"""
        if self.bounding_box.intersects(lod_tile.bounding_box):
            self.lod_tiles.append(lod_tile)
            
            # 递归添加到子节点
            for child in self.children:
                if child:
                    child.add_lod_tile(lod_tile)

class LODScheduler:
    """LOD调度器"""
    
    def __init__(self, stage: Usd.Stage, camera_path: str = "/World/Camera"):
        self.stage = stage
        self.camera_path = camera_path
        self.octree: Optional[OctreeNode] = None
        self.lod_tiles: Dict[str, LODTile] = {}
        
        # LOD配置
        self.lod_config = {
            LODLevel.HIGH: {"screen_error": 1.0, "distance_threshold": 50.0},
            LODLevel.MEDIUM: {"screen_error": 5.0, "distance_threshold": 100.0},
            LODLevel.LOW: {"screen_error": 15.0, "distance_threshold": 200.0},
            LODLevel.VERY_LOW: {"screen_error": 50.0, "distance_threshold": 500.0}
        }
    
    def get_camera_position(self) -> Optional[Gf.Vec3f]:
        """获取相机位置"""
        try:
            camera = self.stage.GetPrimAtPath(self.camera_path)
            if not camera:
                print(f"ERROR: Camera not found at {self.camera_path}")
                return None
            
            xformable = UsdGeom.Xformable(camera)
            if not xformable:
                print("ERROR: Camera is not xformable")
                return None
            
            world_transform = xformable.ComputeLocalToWorldTransform(Usd.TimeCode.Default())
            position = Gf.Vec3f(world_transform[3][0], world_transform[3][1], world_transform[3][2])
            
            return position
        except Exception as e:
            print(f"ERROR: Failed to get camera position: {e}")
            return None
    
    def calculate_screen_error(self, bounding_box: BoundingBox, camera_position: Gf.Vec3f, 
                             fov: float = 60.0, screen_height: int = 1080) -> float:
        """计算屏幕误差"""
        # 计算边界框到相机的距离
        center = bounding_box.center
        distance = math.sqrt(sum((center[i] - camera_position[i])**2 for i in range(3)))
        
        if distance <= 0:
            return float('inf')
        
        # 计算边界框在屏幕上的投影大小
        # 使用简化的投影计算
        fov_rad = math.radians(fov)
        projected_size = (bounding_box.size[0] + bounding_box.size[1]) / (2 * distance * math.tan(fov_rad / 2))
        screen_pixels = projected_size * screen_height
        
        return screen_pixels
    
    def calculate_distance_to_camera(self, bounding_box: BoundingBox, camera_position: Gf.Vec3f) -> float:
        """计算边界框到相机的距离"""
        center = bounding_box.center
        return math.sqrt(sum((center[i] - camera_position[i])**2 for i in range(3)))
    
    def is_in_frustum(self, bounding_box: BoundingBox, camera_position: Gf.Vec3f, 
                     fov: float = 60.0, near_plane: float = 0.1, far_plane: float = 1000.0) -> bool:
        """简化的视锥体剔除"""
        distance = self.calculate_distance_to_camera(bounding_box, camera_position)
        
        # 检查距离范围
        if distance < near_plane or distance > far_plane:
            return False
        
        # 简化的视锥体检查（这里可以扩展为更精确的检查）
        return True
    
    def determine_lod_level(self, screen_error: float, distance: float) -> LODLevel:
        """根据屏幕误差和距离确定LOD级别"""
        for level in [LODLevel.HIGH, LODLevel.MEDIUM, LODLevel.LOW, LODLevel.VERY_LOW]:
            config = self.lod_config[level]
            if screen_error <= config["screen_error"] and distance <= config["distance_threshold"]:
                return level
        
        return LODLevel.VERY_LOW
    
    def build_octree_from_stage(self, max_depth: int = 6, min_area_threshold: float = 100.0):
        """从USD stage构建八叉树"""
        print("Building octree from USD stage...")
        
        # 计算整个场景的边界框
        scene_bounds = self._calculate_scene_bounds()
        if not scene_bounds:
            print("ERROR: Failed to calculate scene bounds")
            return
        
        print(f"Scene bounds: {scene_bounds.min_point} to {scene_bounds.max_point}")
        
        # 创建八叉树根节点
        self.octree = OctreeNode(scene_bounds, max_depth, min_area_threshold)
        self.octree.subdivide()
        
        # 从stage中提取LOD瓦片
        self._extract_lod_tiles_from_stage()
        
        print(f"Octree built with {len(self.lod_tiles)} LOD tiles")
    
    def _calculate_scene_bounds(self) -> Optional[BoundingBox]:
        """计算场景边界框"""
        min_point = Gf.Vec3f(float('inf'), float('inf'), float('inf'))
        max_point = Gf.Vec3f(float('-inf'), float('-inf'), float('-inf'))
        
        found_bounds = False
        
        for prim in self.stage.Traverse():
            # 查找有边界信息的prim
            attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
            attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")
            
            if attr_min and attr_max:
                try:
                    min_bounds = Gf.Vec3f(*attr_min.Get())
                    max_bounds = Gf.Vec3f(*attr_max.Get())
                    
                    min_point = Gf.Vec3f(
                        min(min_point[0], min_bounds[0]),
                        min(min_point[1], min_bounds[1]),
                        min(min_point[2], min_bounds[2])
                    )
                    max_point = Gf.Vec3f(
                        max(max_point[0], max_bounds[0]),
                        max(max_point[1], max_bounds[1]),
                        max(max_point[2], max_bounds[2])
                    )
                    found_bounds = True
                except Exception as e:
                    print(f"Warning: Failed to read bounds for {prim.GetPath()}: {e}")
        
        if not found_bounds:
            print("Warning: No bounds found in stage, using default bounds")
            min_point = Gf.Vec3f(-1000, -1000, -1000)
            max_point = Gf.Vec3f(1000, 1000, 1000)
        
        return BoundingBox(min_point, max_point)
    
    def _extract_lod_tiles_from_stage(self):
        """从stage中提取LOD瓦片"""
        print("Extracting LOD tiles from stage...")
        
        for prim in self.stage.Traverse():
            attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
            attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")
            
            if attr_min and attr_max:
                try:
                    # 检查是否是叶子节点（没有子节点）或者特定的LOD节点
                    prim_path = str(prim.GetPath())
                    
                    # 只处理叶子节点或特定的LOD节点路径
                    # is_leaf = len(list(prim.GetChildren())) == 0
                    is_lod_node = "LOD_" in prim_path
                    
                    if not (is_lod_node):
                        print(f"Skipping non-leaf, non-LOD node: {prim_path}")
                        continue
                    
                    min_bounds = Gf.Vec3f(*attr_min.Get())
                    max_bounds = Gf.Vec3f(*attr_max.Get())
                    bounding_box = BoundingBox(min_bounds, max_bounds)
                    
                    # 创建LOD瓦片（这里使用默认配置，实际应用中应该从数据中读取）
                    tile_id = prim_path
                    lod_tile = LODTile(
                        id=tile_id,
                        bounding_box=bounding_box,
                        lod_level=LODLevel.MEDIUM,  # 默认级别
                        usdz_path=f"{tile_id}.usdz",  # 默认路径
                        screen_error=5.0,
                        distance_threshold=100.0
                    )
                    
                    self.lod_tiles[tile_id] = lod_tile
                    print(f"Extracted LOD tile: {tile_id}")
                    
                    # 添加到八叉树
                    if self.octree:
                        self.octree.add_lod_tile(lod_tile)
                        
                except Exception as e:
                    print(f"Warning: Failed to create LOD tile for {prim.GetPath()}: {e}")
    
    def update_lod_visibility(self, fov: float = 60.0, screen_height: int = 1080):
        """更新LOD可见性"""
        if not self.octree:
            print("ERROR: Octree not built. Call build_octree_from_stage() first.")
            return
        
        camera_position = self.get_camera_position()
        if not camera_position:
            print("ERROR: Failed to get camera position")
            return
        
        print(f"Updating LOD visibility for camera at {camera_position}")
        
        # 递归更新八叉树节点的可见性
        self._update_node_visibility(self.octree, camera_position, fov, screen_height)
    
    def _update_node_visibility(self, node: OctreeNode, camera_position: Gf.Vec3f, 
                              fov: float, screen_height: int):
        """递归更新节点可见性"""
        if not node:
            return
        
        # 检查节点是否在视锥体内
        if not self.is_in_frustum(node.bounding_box, camera_position, fov):
            # 隐藏节点中的所有LOD瓦片
            self._hide_lod_tiles(node.lod_tiles)
            return
        
        # 计算屏幕误差和距离
        screen_error = self.calculate_screen_error(node.bounding_box, camera_position, fov, screen_height)
        distance = self.calculate_distance_to_camera(node.bounding_box, camera_position)
        
        # 确定LOD级别
        lod_level = self.determine_lod_level(screen_error, distance)
        
        # 检查是否需要细分
        should_subdivide = (node.children[0] is not None and 
                           node.bounding_box.area > node.min_area_threshold and
                           screen_error > self.lod_config[LODLevel.HIGH]["screen_error"])
        
        if should_subdivide:
            # 递归处理子节点
            for child in node.children:
                if child:
                    self._update_node_visibility(child, camera_position, fov, screen_height)
        else:
            # 显示当前节点对应的LOD级别
            self._show_lod_tiles_for_level(node.lod_tiles, lod_level)
    
    def _hide_lod_tiles(self, lod_tiles: List[LODTile]):
        """隐藏LOD瓦片"""
        for tile in lod_tiles:
            # 查找对应的prim并隐藏
            prim = self.stage.GetPrimAtPath(tile.id)
            if prim:
                parent2 = prim.GetParent().GetParent()
                if parent2:
                    vis_attr = UsdGeom.Imageable(parent2).GetVisibilityAttr()
                    vis_attr.Set(UsdGeom.Tokens.invisible)
    
    def _show_lod_tiles_for_level(self, lod_tiles: List[LODTile], target_level: LODLevel):
        """显示指定级别的LOD瓦片"""
        for tile in lod_tiles:
            # 查找对应的prim
            prim = self.stage.GetPrimAtPath(tile.id)
            if prim:
                parent2 = prim.GetParent().GetParent()
                if parent2:
                    vis_attr = UsdGeom.Imageable(parent2).GetVisibilityAttr()
                    
                    # 如果瓦片的LOD级别匹配目标级别，则显示
                    if tile.lod_level == target_level:
                        vis_attr.Set(UsdGeom.Tokens.inherited)
                    else:
                        vis_attr.Set(UsdGeom.Tokens.invisible)
    
    def save_lod_config(self, file_path: str):
        """保存LOD配置到文件"""
        config_data = {
            "lod_config": {level.value: config for level, config in self.lod_config.items()},
            "lod_tiles": {
                tile_id: {
                    "id": tile.id,
                    "bounding_box": {
                        "min": [tile.bounding_box.min_point[i] for i in range(3)],
                        "max": [tile.bounding_box.max_point[i] for i in range(3)]
                    },
                    "lod_level": tile.lod_level.value,
                    "usdz_path": tile.usdz_path,
                    "screen_error": tile.screen_error,
                    "distance_threshold": tile.distance_threshold
                }
                for tile_id, tile in self.lod_tiles.items()
            }
        }
        
        with open(file_path, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        print(f"LOD configuration saved to {file_path}")
    
    def load_lod_config(self, file_path: str):
        """从文件加载LOD配置"""
        try:
            with open(file_path, 'r') as f:
                config_data = json.load(f)
            
            # 加载LOD配置
            self.lod_config = {
                LODLevel(level): config for level, config in config_data["lod_config"].items()
            }
            
            # 加载LOD瓦片
            self.lod_tiles.clear()
            for tile_id, tile_data in config_data["lod_tiles"].items():
                bounding_box = BoundingBox(
                    Gf.Vec3f(*tile_data["bounding_box"]["min"]),
                    Gf.Vec3f(*tile_data["bounding_box"]["max"])
                )
                
                lod_tile = LODTile(
                    id=tile_data["id"],
                    bounding_box=bounding_box,
                    lod_level=LODLevel(tile_data["lod_level"]),
                    usdz_path=tile_data["usdz_path"],
                    screen_error=tile_data["screen_error"],
                    distance_threshold=tile_data["distance_threshold"]
                )
                
                self.lod_tiles[tile_id] = lod_tile
            
            print(f"LOD configuration loaded from {file_path}")
            
        except Exception as e:
            print(f"ERROR: Failed to load LOD configuration from {file_path}: {e}")

def main():
    """主函数示例"""
    stage = omni.usd.get_context().get_stage()
    
    # 创建LOD调度器
    scheduler = LODScheduler(stage, camera_path="/World/Camera")
    
    # 构建八叉树
    scheduler.build_octree_from_stage(max_depth=6, min_area_threshold=100.0)
    
    # 更新LOD可见性
    scheduler.update_lod_visibility(fov=60.0, screen_height=1080)
    
    # 保存配置（可选）
    # scheduler.save_lod_config("lod_config.json")

if __name__ == "__main__":
    main() 