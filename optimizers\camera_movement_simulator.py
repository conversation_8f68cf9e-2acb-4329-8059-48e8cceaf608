# 相机移动模拟脚本
# 独立运行的相机移动模拟器，用于测试自动相机优化器

from pxr import Usd, UsdGeom, Gf
import omni.usd
import omni.timeline
import time
import math
import argparse

class CameraMovementConfig:
    """相机移动配置类"""
    def __init__(self):
        # 相机配置
        self.camera_path = "/World/Camera"
        
        # 移动配置
        self.start_position = Gf.Vec3f(0, 0, 10)  # 起始位置
        self.target_position = Gf.Vec3f(20, 0, 10)  # 目标位置
        self.duration = 30.0  # 移动持续时间（秒）
        self.loop = True  # 是否循环移动
        self.update_interval = 0.05  # 更新间隔（秒）
        
        # 移动模式
        self.movement_type = "linear"  # "linear", "sine", "circular"
        
        # 圆形移动配置（当movement_type为"circular"时使用）
        self.circle_center = Gf.Vec3f(10, 0, 10)  # 圆心
        self.circle_radius = 10.0  # 半径
        self.circle_height = 5.0  # 高度变化范围

def simulate_linear_movement(camera, config):
    """线性移动模拟"""
    start_time = time.time()
    last_update_time = start_time
    
    try:
        while True:
            current_time = time.time()
            elapsed_time = current_time - start_time
            
            # 计算移动进度 (0.0 到 1.0)
            if config.duration > 0:
                progress = min(elapsed_time / config.duration, 1.0)
            else:
                progress = 1.0
            
            # 使用正弦缓动函数使移动更平滑
            smooth_progress = 0.5 * (1 - math.cos(progress * math.pi))
            
            # 计算当前位置
            current_pos = Gf.Vec3f(
                config.start_position[0] + (config.target_position[0] - config.start_position[0]) * smooth_progress,
                config.start_position[1] + (config.target_position[1] - config.start_position[1]) * smooth_progress,
                config.start_position[2] + (config.target_position[2] - config.start_position[2]) * smooth_progress
            )
            
            # 设置相机位置
            camera.AddTranslateOp().Set(current_pos)
            
            # 每0.1秒更新一次位置
            if current_time - last_update_time >= 0.1:
                print(f"Camera position: {current_pos} (progress: {progress:.2f})")
                last_update_time = current_time
            
            # 检查是否完成一次移动
            if progress >= 1.0:
                if config.loop:
                    # 循环模式：交换起始和目标位置
                    config.start_position, config.target_position = config.target_position, config.start_position
                    start_time = current_time
                    print("\n--- Reversing camera movement ---")
                else:
                    # 非循环模式：结束
                    print("\n--- Camera movement completed ---")
                    break
            
            time.sleep(config.update_interval)
            
    except KeyboardInterrupt:
        print("\n--- Camera movement simulation stopped by user ---")
    except Exception as e:
        print(f"ERROR in camera movement simulation: {e}")

def simulate_circular_movement(camera, config):
    """圆形移动模拟"""
    start_time = time.time()
    last_update_time = start_time
    
    try:
        while True:
            current_time = time.time()
            elapsed_time = current_time - start_time
            
            # 计算角度（每秒旋转一圈）
            angle = (elapsed_time * 2 * math.pi) / config.duration
            
            # 计算圆形轨迹上的位置
            x = config.circle_center[0] + config.circle_radius * math.cos(angle)
            y = config.circle_center[1] + config.circle_radius * math.sin(angle)
            z = config.circle_center[2] + config.circle_height * math.sin(angle * 2)  # 上下波动
            
            current_pos = Gf.Vec3f(x, y, z)
            
            # 设置相机位置
            camera.AddTranslateOp().Set(current_pos)
            
            # 每0.1秒更新一次位置
            if current_time - last_update_time >= 0.1:
                print(f"Camera position: {current_pos} (angle: {angle:.2f} rad)")
                last_update_time = current_time
            
            # 检查是否应该停止（非循环模式）
            if not config.loop and elapsed_time >= config.duration:
                print("\n--- Camera movement completed ---")
                break
            
            time.sleep(config.update_interval)
            
    except KeyboardInterrupt:
        print("\n--- Camera movement simulation stopped by user ---")
    except Exception as e:
        print(f"ERROR in camera movement simulation: {e}")

def simulate_sine_movement(camera, config):
    """正弦波移动模拟"""
    start_time = time.time()
    last_update_time = start_time
    
    try:
        while True:
            current_time = time.time()
            elapsed_time = current_time - start_time
            
            # 计算移动进度
            progress = (elapsed_time / config.duration) if config.duration > 0 else 0
            
            # 使用正弦波计算位置
            x = config.start_position[0] + (config.target_position[0] - config.start_position[0]) * progress
            y = config.start_position[1] + 5 * math.sin(progress * 4 * math.pi)  # 左右摆动
            z = config.start_position[2] + 3 * math.sin(progress * 2 * math.pi)  # 上下摆动
            
            current_pos = Gf.Vec3f(x, y, z)
            
            # 设置相机位置
            camera.AddTranslateOp().Set(current_pos)
            
            # 每0.1秒更新一次位置
            if current_time - last_update_time >= 0.1:
                print(f"Camera position: {current_pos} (progress: {progress:.2f})")
                last_update_time = current_time
            
            # 检查是否完成一次移动
            if progress >= 1.0:
                if config.loop:
                    start_time = current_time
                    print("\n--- Restarting sine movement ---")
                else:
                    print("\n--- Camera movement completed ---")
                    break
            
            time.sleep(config.update_interval)
            
    except KeyboardInterrupt:
        print("\n--- Camera movement simulation stopped by user ---")
    except Exception as e:
        print(f"ERROR in camera movement simulation: {e}")

def simulate_camera_movement(config):
    """
    模拟相机移动
    
    Args:
        config: CameraMovementConfig实例
    """
    print(f"Starting camera movement simulation...")
    print(f"  Camera path: {config.camera_path}")
    print(f"  Movement type: {config.movement_type}")
    print(f"  Duration: {config.duration}s")
    print(f"  Loop: {config.loop}")
    
    if config.movement_type == "linear":
        print(f"  Start position: {config.start_position}")
        print(f"  Target position: {config.target_position}")
    elif config.movement_type == "circular":
        print(f"  Circle center: {config.circle_center}")
        print(f"  Circle radius: {config.circle_radius}")
        print(f"  Circle height: {config.circle_height}")
    elif config.movement_type == "sine":
        print(f"  Start position: {config.start_position}")
        print(f"  Target position: {config.target_position}")
    
    # 获取stage和相机
    stage = omni.usd.get_context().get_stage()
    camera = stage.GetPrimAtPath(config.camera_path)
    
    if not camera:
        print(f"ERROR: Camera not found at {config.camera_path}")
        return
    
    # 获取相机的xformable
    xformable = UsdGeom.Xformable(camera)
    if not xformable:
        print("ERROR: Camera is not xformable")
        return
    
    # 获取时间轴
    timeline = omni.timeline.get_timeline_interface()
    
    # 确保运行时已启动
    if not timeline.is_playing():
        print("Starting runtime...")
        timeline.play()
    
    # 根据移动类型选择相应的模拟函数
    if config.movement_type == "linear":
        simulate_linear_movement(xformable, config)
    elif config.movement_type == "circular":
        simulate_circular_movement(xformable, config)
    elif config.movement_type == "sine":
        simulate_sine_movement(xformable, config)
    else:
        print(f"ERROR: Unknown movement type: {config.movement_type}")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Camera Movement Simulator')
    
    # 相机路径
    parser.add_argument('--camera-path', type=str, default="/World/Camera",
                       help='Camera prim path (default: /World/Camera)')
    
    # 移动类型
    parser.add_argument('--movement-type', type=str, choices=['linear', 'circular', 'sine'], 
                       default='linear', help='Movement type (default: linear)')
    
    # 线性移动参数
    parser.add_argument('--start-x', type=float, default=0.0, help='Start X position')
    parser.add_argument('--start-y', type=float, default=0.0, help='Start Y position')
    parser.add_argument('--start-z', type=float, default=10.0, help='Start Z position')
    parser.add_argument('--target-x', type=float, default=20.0, help='Target X position')
    parser.add_argument('--target-y', type=float, default=0.0, help='Target Y position')
    parser.add_argument('--target-z', type=float, default=10.0, help='Target Z position')
    
    # 圆形移动参数
    parser.add_argument('--circle-center-x', type=float, default=10.0, help='Circle center X')
    parser.add_argument('--circle-center-y', type=float, default=0.0, help='Circle center Y')
    parser.add_argument('--circle-center-z', type=float, default=10.0, help='Circle center Z')
    parser.add_argument('--circle-radius', type=float, default=10.0, help='Circle radius')
    parser.add_argument('--circle-height', type=float, default=5.0, help='Circle height variation')
    
    # 通用参数
    parser.add_argument('--duration', type=float, default=30.0, help='Movement duration in seconds')
    parser.add_argument('--no-loop', action='store_true', help='Disable looping')
    parser.add_argument('--update-interval', type=float, default=0.05, help='Update interval in seconds')
    
    return parser.parse_args()

def main():
    """主函数"""
    print("=== Camera Movement Simulator ===")
    
    # 解析命令行参数
    args = parse_arguments()
    
    # 创建配置
    config = CameraMovementConfig()
    config.camera_path = args.camera_path
    config.movement_type = args.movement_type
    config.duration = args.duration
    config.loop = not args.no_loop
    config.update_interval = args.update_interval
    
    # 设置位置参数
    if args.movement_type == "linear":
        config.start_position = Gf.Vec3f(args.start_x, args.start_y, args.start_z)
        config.target_position = Gf.Vec3f(args.target_x, args.target_y, args.target_z)
    elif args.movement_type == "circular":
        config.circle_center = Gf.Vec3f(args.circle_center_x, args.circle_center_y, args.circle_center_z)
        config.circle_radius = args.circle_radius
        config.circle_height = args.circle_height
    elif args.movement_type == "sine":
        config.start_position = Gf.Vec3f(args.start_x, args.start_y, args.start_z)
        config.target_position = Gf.Vec3f(args.target_x, args.target_y, args.target_z)
    
    # 运行模拟
    simulate_camera_movement(config)

if __name__ == "__main__":
    main() 