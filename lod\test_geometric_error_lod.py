"""
测试几何误差LOD系统
验证重构后的LOD计算逻辑
"""

import math
import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def calculate_screen_metric(geometric_error, distance, fov=60.0, screen_height=1080):
    """
    计算几何误差投影到屏幕的度量值
    
    Args:
        geometric_error: 几何误差（世界单位，米）
        distance: 到目标的距离（米）
        fov: 视场角（度）
        screen_height: 屏幕高度（像素）
    
    Returns:
        tuple: (screen_pixels, screen_ratio)
            - screen_pixels: 屏幕误差（像素）
            - screen_ratio: 屏幕误差占屏幕高度的比例
    """
    if distance <= 0:
        return float('inf'), float('inf')
    
    # 计算焦距（像素单位）
    focal_len_px = (screen_height / 2) / math.tan(math.radians(fov / 2))
    
    # 几何误差投影到屏幕的像素误差
    screen_pixels = (geometric_error / distance) * focal_len_px
    
    # 计算屏幕比例
    screen_ratio = screen_pixels / screen_height
    
    return screen_pixels, screen_ratio

def determine_lod_by_screen_metric(screen_pixels, screen_ratio, thresholds):
    """
    根据屏幕度量值确定LOD级别

    Args:
        screen_pixels: 屏幕误差（像素）
        screen_ratio: 屏幕误差占屏幕高度的比例
        thresholds: LOD阈值字典，格式：{"High": threshold, "Medium": threshold, "Low": threshold}

    Returns:
        str: LOD级别 ("High", "Medium", "Low")
    """
    # 自动判断阈值单位（<=1 则按比例，否则按像素）
    def _get_metric_value(threshold_value):
        if threshold_value <= 1.0:
            # 比例阈值
            return screen_ratio
        else:
            # 像素阈值
            return screen_pixels

    # 获取各级别阈值
    high_threshold = thresholds.get("High", 2.0)
    medium_threshold = thresholds.get("Medium", 8.0)
    low_threshold = thresholds.get("Low", 20.0)

    # 获取对应的度量值
    metric_value = _get_metric_value(high_threshold)  # 使用High LOD的单位类型

    # 正确的LOD判断逻辑：屏幕误差小于等于阈值时使用该LOD
    # 从最严格（High）到最宽松（Low）检查
    if metric_value <= high_threshold:
        return "High"
    elif metric_value <= medium_threshold:
        return "Medium"
    elif metric_value <= low_threshold:
        return "Low"
    else:
        # 超过所有阈值，使用最低质量
        return "Low"

def test_geometric_error_lod():
    """测试几何误差LOD系统"""
    print("=== 测试几何误差LOD系统 ===\n")
    
    # 配置参数
    geometric_errors = {
        "High": 0.5,    # 高质量LOD：0.5米几何误差
        "Medium": 2.0,  # 中质量LOD：2.0米几何误差  
        "Low": 8.0      # 低质量LOD：8.0米几何误差
    }
    
    # 屏幕误差阈值（像素）
    pixel_thresholds = {
        "High": 2.0,    # 2像素
        "Medium": 8.0,  # 8像素
        "Low": 20.0     # 20像素
    }
    
    # 屏幕误差阈值（比例）
    ratio_thresholds = {
        "High": 0.02,   # 2%屏幕高度
        "Medium": 0.08, # 8%屏幕高度
        "Low": 0.2      # 20%屏幕高度
    }
    
    # 测试距离
    test_distances = [10, 25, 50, 100, 200, 500, 1000]
    
    print("几何误差配置:")
    for lod, error in geometric_errors.items():
        print(f"  {lod}: {error}m")
    
    print(f"\n像素阈值配置:")
    for lod, threshold in pixel_thresholds.items():
        print(f"  {lod}: {threshold}px")
    
    print(f"\n比例阈值配置:")
    for lod, threshold in ratio_thresholds.items():
        print(f"  {lod}: {threshold:.3f} ({threshold*100:.1f}%)")
    
    print(f"\n{'='*80}")
    print(f"{'距离(m)':<8} {'High LOD':<20} {'Medium LOD':<20} {'Low LOD':<20} {'像素阈值':<12} {'比例阈值':<12}")
    print(f"{'='*80}")
    
    for distance in test_distances:
        # 计算每个LOD的屏幕度量值
        high_pixels, high_ratio = calculate_screen_metric(geometric_errors["High"], distance)
        medium_pixels, medium_ratio = calculate_screen_metric(geometric_errors["Medium"], distance)
        low_pixels, low_ratio = calculate_screen_metric(geometric_errors["Low"], distance)
        
        # 使用High LOD的度量值来确定LOD级别
        pixel_lod = determine_lod_by_screen_metric(high_pixels, high_ratio, pixel_thresholds)
        ratio_lod = determine_lod_by_screen_metric(high_pixels, high_ratio, ratio_thresholds)
        
        print(f"{distance:<8} "
              f"{high_pixels:>6.1f}px({high_ratio:>6.4f})<{'':<6} "
              f"{medium_pixels:>6.1f}px({medium_ratio:>6.4f})<{'':<6} "
              f"{low_pixels:>6.1f}px({low_ratio:>6.4f})<{'':<6} "
              f"{pixel_lod:<12} "
              f"{ratio_lod:<12}")
    
    print(f"{'='*80}")
    
    # 对比旧方法（使用整个region尺寸）
    print(f"\n对比：使用整个region尺寸的旧方法")
    print(f"假设region最大尺寸为200m")
    region_max_dimension = 200.0
    
    print(f"\n{'距离(m)':<8} {'旧方法屏幕误差(px)':<20} {'新方法High LOD(px)':<20} {'差异倍数':<12}")
    print(f"{'-'*60}")
    
    for distance in test_distances:
        # 旧方法计算
        fov = 60.0
        screen_height = 1080
        focal_len_px = (screen_height / 2) / math.tan(math.radians(fov / 2))
        old_screen_error = (region_max_dimension / distance) * focal_len_px
        
        # 新方法计算
        new_screen_error, _ = calculate_screen_metric(geometric_errors["High"], distance)
        
        # 差异倍数
        ratio = old_screen_error / new_screen_error if new_screen_error > 0 else float('inf')
        
        print(f"{distance:<8} "
              f"{old_screen_error:>18.1f} "
              f"{new_screen_error:>18.1f} "
              f"{ratio:>10.1f}x")

def test_threshold_units():
    """测试阈值单位自动判断"""
    print(f"\n\n=== 测试阈值单位自动判断 ===")
    
    # 测试数据
    test_cases = [
        {"distance": 100, "geometric_error": 0.5},
        {"distance": 50, "geometric_error": 2.0},
        {"distance": 200, "geometric_error": 8.0},
    ]
    
    # 不同的阈值配置
    threshold_configs = [
        {"name": "像素阈值", "thresholds": {"High": 2.0, "Medium": 8.0, "Low": 20.0}},
        {"name": "比例阈值", "thresholds": {"High": 0.02, "Medium": 0.08, "Low": 0.2}},
        {"name": "混合阈值", "thresholds": {"High": 0.02, "Medium": 8.0, "Low": 20.0}},
    ]
    
    for config in threshold_configs:
        print(f"\n{config['name']}:")
        for lod, threshold in config['thresholds'].items():
            unit = "比例" if threshold <= 1.0 else "像素"
            print(f"  {lod}: {threshold} ({unit})")
        
        print(f"\n{'距离(m)':<8} {'几何误差(m)':<12} {'屏幕像素':<12} {'屏幕比例':<12} {'LOD级别':<8}")
        print(f"{'-'*60}")
        
        for case in test_cases:
            distance = case["distance"]
            geometric_error = case["geometric_error"]
            
            screen_pixels, screen_ratio = calculate_screen_metric(geometric_error, distance)
            lod = determine_lod_by_screen_metric(screen_pixels, screen_ratio, config['thresholds'])
            
            print(f"{distance:<8} "
                  f"{geometric_error:<12} "
                  f"{screen_pixels:>10.1f} "
                  f"{screen_ratio:>10.4f} "
                  f"{lod:<8}")

if __name__ == "__main__":
    test_geometric_error_lod()
    test_threshold_units()
    
    print(f"\n\n=== 总结 ===")
    print("✅ 重构完成的改进:")
    print("1. 使用几何误差（世界单位）替代整个region尺寸")
    print("2. 支持像素和比例两种阈值单位")
    print("3. 自动判断阈值单位（<=1为比例，>1为像素）")
    print("4. 提供更合理的屏幕误差计算")
    print("5. 保持向后兼容的距离回退机制")
    
    print(f"\n建议配置:")
    print("几何误差: High=0.5m, Medium=2.0m, Low=8.0m")
    print("像素阈值: High=2px, Medium=8px, Low=20px")
    print("比例阈值: High=0.02(2%), Medium=0.08(8%), Low=0.2(20%)")
