"""
测试正确的LOD选择逻辑
验证修复后的"从低到高选择第一个满足条件的"策略
"""

import math

def calculate_sse(geometric_error, distance_to_camera, screen_width=1920, h_fov=60.0):
    """计算屏幕空间误差(SSE)"""
    if distance_to_camera <= 0:
        return float('inf')
    
    sse = (geometric_error * screen_width) / (2 * distance_to_camera * math.tan(math.radians(h_fov / 2)))
    return sse

def select_lod_by_sse_correct(lod_configs, distance_to_camera, maximum_sse=8.0):
    """正确的SSE选择策略：从低到高选择第一个满足条件的"""
    lod_levels = ["Low", "Medium", "High"]
    selected_lod = "Low"
    
    lod_sse_info = {}
    for lod_name in lod_levels:
        geometric_error = lod_configs.get(lod_name, 1.0)
        sse = calculate_sse(geometric_error, distance_to_camera)
        lod_sse_info[lod_name] = {
            'geometric_error': geometric_error,
            'sse': sse,
            'acceptable': sse <= maximum_sse
        }
    
    # 正确策略：从低细节到高细节遍历，选择第一个满足条件的
    for lod_name in lod_levels:  # ["Low", "Medium", "High"]
        sse_info = lod_sse_info[lod_name]
        if sse_info['acceptable']:
            selected_lod = lod_name
            break
    
    # 如果没有LOD满足条件，选择SSE最小的（最高质量）
    if selected_lod == "Low" and not lod_sse_info["Low"]["acceptable"]:
        min_sse_lod = min(lod_sse_info.keys(), key=lambda x: lod_sse_info[x]['sse'])
        selected_lod = min_sse_lod
    
    return selected_lod, lod_sse_info

def select_lod_by_sse_wrong(lod_configs, distance_to_camera, maximum_sse=8.0):
    """错误的SSE选择策略：从高到低选择第一个满足条件的"""
    lod_levels = ["Low", "Medium", "High"]
    selected_lod = "Low"
    
    lod_sse_info = {}
    for lod_name in lod_levels:
        geometric_error = lod_configs.get(lod_name, 1.0)
        sse = calculate_sse(geometric_error, distance_to_camera)
        lod_sse_info[lod_name] = {
            'geometric_error': geometric_error,
            'sse': sse,
            'acceptable': sse <= maximum_sse
        }
    
    # 错误策略：从高细节到低细节遍历，选择第一个满足条件的
    for lod_name in reversed(lod_levels):  # ["High", "Medium", "Low"]
        sse_info = lod_sse_info[lod_name]
        if sse_info['acceptable']:
            selected_lod = lod_name
            break
    
    return selected_lod, lod_sse_info

def test_lod_selection_comparison():
    """对比正确和错误的LOD选择策略"""
    print("=== 正确 vs 错误的LOD选择策略对比 ===\n")
    
    # 当前配置（用户修改后的）
    config = {
        "High": 0.5,
        "Medium": 2.0,
        "Low": 4.0
    }
    
    sse_threshold = 8.0
    test_distances = [50, 100, 200, 400, 800, 1600]
    
    print(f"配置: High=0.5m, Medium=2.0m, Low=4.0m")
    print(f"SSE阈值: {sse_threshold}px")
    
    print(f"\n{'距离(m)':<8} {'High SSE':<10} {'Medium SSE':<12} {'Low SSE':<10} {'错误策略':<8} {'正确策略':<8}")
    print(f"{'-'*70}")
    
    for distance in test_distances:
        wrong_lod, wrong_info = select_lod_by_sse_wrong(config, distance, sse_threshold)
        correct_lod, correct_info = select_lod_by_sse_correct(config, distance, sse_threshold)
        
        high_sse = wrong_info["High"]["sse"]
        medium_sse = wrong_info["Medium"]["sse"]
        low_sse = wrong_info["Low"]["sse"]
        
        print(f"{distance:<8} {high_sse:<10.1f} {medium_sse:<12.1f} {low_sse:<10.1f} {wrong_lod:<8} {correct_lod:<8}")

def test_realistic_scenarios():
    """测试现实场景下的LOD选择"""
    print(f"\n\n=== 现实场景LOD选择测试 ===\n")
    
    config = {"High": 0.5, "Medium": 2.0, "Low": 4.0}
    sse_threshold = 8.0
    
    scenarios = [
        {"name": "近距离观察", "distances": [20, 30, 40, 50]},
        {"name": "中距离观察", "distances": [80, 120, 160, 200]},
        {"name": "远距离观察", "distances": [300, 500, 800, 1200]},
        {"name": "超远距离", "distances": [2000, 3000, 5000, 8000]}
    ]
    
    for scenario in scenarios:
        print(f"{scenario['name']}:")
        print(f"{'距离(m)':<8} {'Low SSE':<10} {'Medium SSE':<12} {'High SSE':<10} {'选择LOD':<8} {'说明':<20}")
        print(f"{'-'*75}")
        
        for distance in scenario['distances']:
            selected_lod, lod_info = select_lod_by_sse_correct(config, distance, sse_threshold)
            
            low_sse = lod_info["Low"]["sse"]
            medium_sse = lod_info["Medium"]["sse"]
            high_sse = lod_info["High"]["sse"]
            
            # 分析选择原因
            if low_sse <= sse_threshold:
                reason = "Low满足，选最低质量"
            elif medium_sse <= sse_threshold:
                reason = "Medium满足，选中等质量"
            elif high_sse <= sse_threshold:
                reason = "High满足，选高质量"
            else:
                reason = "都不满足，选最高质量"
            
            print(f"{distance:<8} {low_sse:<10.1f} {medium_sse:<12.1f} {high_sse:<10.1f} {selected_lod:<8} {reason}")
        print()

def analyze_lod_transition():
    """分析LOD切换点"""
    print(f"=== LOD切换点分析 ===\n")
    
    config = {"High": 0.5, "Medium": 2.0, "Low": 4.0}
    sse_threshold = 8.0
    
    print(f"配置: {config}")
    print(f"阈值: {sse_threshold}px")
    
    # 计算各LOD的切换距离
    # SSE = (geometric_error × 1920) / (2 × distance × tan(30°))
    # distance = (geometric_error × 1920) / (2 × SSE × tan(30°))
    
    tan_30 = math.tan(math.radians(30))
    screen_width = 1920
    
    low_transition = (config["Low"] * screen_width) / (2 * sse_threshold * tan_30)
    medium_transition = (config["Medium"] * screen_width) / (2 * sse_threshold * tan_30)
    high_transition = (config["High"] * screen_width) / (2 * sse_threshold * tan_30)
    
    print(f"\n理论切换距离:")
    print(f"Low LOD切换距离: {low_transition:.1f}m (距离>{low_transition:.1f}m时选择Low)")
    print(f"Medium LOD切换距离: {medium_transition:.1f}m (距离>{medium_transition:.1f}m时选择Medium)")
    print(f"High LOD切换距离: {high_transition:.1f}m (距离>{high_transition:.1f}m时选择High)")
    
    print(f"\n实际LOD选择区间:")
    print(f"距离 > {low_transition:.1f}m: 选择Low LOD")
    print(f"距离 {medium_transition:.1f}m - {low_transition:.1f}m: 选择Medium LOD")
    print(f"距离 {high_transition:.1f}m - {medium_transition:.1f}m: 选择High LOD")
    print(f"距离 < {high_transition:.1f}m: 选择High LOD (都不满足阈值)")
    
    # 验证切换点
    print(f"\n验证切换点:")
    test_distances = [
        high_transition - 10,
        high_transition + 10,
        medium_transition - 10,
        medium_transition + 10,
        low_transition - 10,
        low_transition + 10
    ]
    
    print(f"{'距离(m)':<8} {'选择LOD':<8} {'Low SSE':<10} {'Medium SSE':<12} {'High SSE':<10}")
    print(f"{'-'*60}")
    
    for distance in test_distances:
        if distance > 0:
            selected_lod, lod_info = select_lod_by_sse_correct(config, distance, sse_threshold)
            low_sse = lod_info["Low"]["sse"]
            medium_sse = lod_info["Medium"]["sse"]
            high_sse = lod_info["High"]["sse"]
            
            print(f"{distance:<8.1f} {selected_lod:<8} {low_sse:<10.1f} {medium_sse:<12.1f} {high_sse:<10.1f}")

def test_performance_implications():
    """测试性能影响"""
    print(f"\n\n=== 性能影响分析 ===\n")
    
    config = {"High": 0.5, "Medium": 2.0, "Low": 4.0}
    sse_threshold = 8.0
    
    distances = [50, 100, 200, 500, 1000, 2000]
    
    print("LOD选择的性能含义:")
    print("- High LOD: 最高质量，最大性能开销")
    print("- Medium LOD: 中等质量，中等性能开销")
    print("- Low LOD: 最低质量，最小性能开销")
    
    print(f"\n{'距离(m)':<8} {'选择LOD':<8} {'性能开销':<10} {'质量水平':<10}")
    print(f"{'-'*45}")
    
    performance_map = {"High": "高", "Medium": "中", "Low": "低"}
    quality_map = {"High": "最高", "Medium": "中等", "Low": "最低"}
    
    for distance in distances:
        selected_lod, _ = select_lod_by_sse_correct(config, distance, sse_threshold)
        performance = performance_map[selected_lod]
        quality = quality_map[selected_lod]
        
        print(f"{distance:<8} {selected_lod:<8} {performance:<10} {quality:<10}")

if __name__ == "__main__":
    test_lod_selection_comparison()
    test_realistic_scenarios()
    analyze_lod_transition()
    test_performance_implications()
    
    print(f"\n{'='*60}")
    print("🎯 关键发现:")
    print("1. 错误策略：无论多远都选择High LOD（性能浪费）")
    print("2. 正确策略：根据距离合理选择LOD（性能优化）")
    print("3. LOD切换点：Low>416m, Medium 104-416m, High<104m")
    print("4. 远距离使用Low LOD可以显著提升性能")
    
    print(f"\n💡 修复效果:")
    print("- 远距离(>416m): 从High LOD → Low LOD (性能提升)")
    print("- 中距离(104-416m): 从High LOD → Medium LOD (平衡性能)")
    print("- 近距离(<104m): 保持High LOD (保证质量)")
    print("- 现在LOD选择符合性能优化原则")
