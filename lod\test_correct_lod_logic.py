"""
测试正确的LOD逻辑
验证屏幕误差与距离的关系，以及正确的LOD判断逻辑
"""

import math

def calculate_screen_metric(geometric_error, distance, fov=60.0, screen_height=1080):
    """计算几何误差投影到屏幕的度量值"""
    if distance <= 0:
        return float('inf'), float('inf')
    
    # 计算焦距（像素单位）
    focal_len_px = (screen_height / 2) / math.tan(math.radians(fov / 2))
    
    # 几何误差投影到屏幕的像素误差
    screen_pixels = (geometric_error / distance) * focal_len_px
    
    # 计算屏幕比例
    screen_ratio = screen_pixels / screen_height
    
    return screen_pixels, screen_ratio

def determine_lod_by_screen_metric(screen_pixels, screen_ratio, thresholds):
    """根据屏幕度量值确定LOD级别"""
    # 自动判断阈值单位（<=1 则按比例，否则按像素）
    def _get_metric_value(threshold_value):
        if threshold_value <= 1.0:
            return screen_ratio
        else:
            return screen_pixels
    
    # 获取各级别阈值
    high_threshold = thresholds.get("High", 2.0)
    medium_threshold = thresholds.get("Medium", 8.0)
    low_threshold = thresholds.get("Low", 20.0)
    
    # 获取对应的度量值（使用High LOD的单位类型）
    metric_value = _get_metric_value(high_threshold)
    
    # 正确的LOD判断逻辑：屏幕误差大于等于阈值时使用该LOD
    # 屏幕误差大 → 需要高质量LOD
    if metric_value >= high_threshold:
        return "High"
    elif metric_value >= medium_threshold:
        return "Medium"
    elif metric_value >= low_threshold:
        return "Low"
    else:
        # 屏幕误差很小，使用最低质量即可
        return "Low"

def test_distance_vs_screen_error():
    """测试距离与屏幕误差的关系"""
    print("=== 距离与屏幕误差关系测试 ===\n")
    
    geometric_error = 0.5  # High LOD的几何误差
    
    print("几何误差: 0.5米")
    print("公式: 屏幕误差 = (几何误差 / 距离) × 焦距")
    print("焦距 ≈ 935像素 (FOV=60°, 屏幕高度=1080px)\n")
    
    print(f"{'距离(m)':<8} {'屏幕误差(px)':<14} {'物理解释':<30}")
    print(f"{'-'*60}")
    
    distances = [10, 25, 50, 100, 200, 500, 1000]
    
    for distance in distances:
        screen_pixels, screen_ratio = calculate_screen_metric(geometric_error, distance)
        
        if distance <= 50:
            explanation = "近距离 → 误差大 → 需要高质量"
        elif distance <= 200:
            explanation = "中距离 → 误差中等 → 中等质量"
        else:
            explanation = "远距离 → 误差小 → 低质量即可"
        
        print(f"{distance:<8} {screen_pixels:>12.1f} {explanation}")
    
    print(f"\n✅ 结论: 距离越近，屏幕误差越大 - 这是正确的物理现象！")

def test_lod_logic():
    """测试LOD判断逻辑"""
    print(f"\n\n=== LOD判断逻辑测试 ===\n")
    
    geometric_error = 0.5  # High LOD的几何误差
    
    # 像素阈值配置
    thresholds = {
        "High": 2.0,    # 2像素
        "Medium": 8.0,  # 8像素
        "Low": 20.0     # 20像素
    }
    
    print("阈值配置:")
    print(f"High LOD: >= {thresholds['High']}px")
    print(f"Medium LOD: >= {thresholds['Medium']}px")
    print(f"Low LOD: >= {thresholds['Low']}px")
    print(f"Very Low: < {thresholds['Low']}px\n")
    
    print(f"{'距离(m)':<8} {'屏幕误差(px)':<14} {'LOD级别':<10} {'逻辑说明':<30}")
    print(f"{'-'*70}")
    
    test_distances = [25, 50, 100, 200, 300, 500, 1000]
    
    for distance in test_distances:
        screen_pixels, screen_ratio = calculate_screen_metric(geometric_error, distance)
        lod = determine_lod_by_screen_metric(screen_pixels, screen_ratio, thresholds)
        
        # 逻辑说明
        if screen_pixels >= 8.0:
            logic = f"≥8px → High (误差大需高质量)"
        elif screen_pixels >= 2.0:
            logic = f"≥2px → Medium (误差中等)"
        else:
            logic = f"<2px → Low (误差小用低质量)"
        
        print(f"{distance:<8} {screen_pixels:>12.1f} {lod:<10} {logic}")

def test_threshold_units():
    """测试不同阈值单位"""
    print(f"\n\n=== 阈值单位测试 ===\n")
    
    # 比例阈值配置
    ratio_thresholds = {
        "High": 0.008,   # 0.8%屏幕高度
        "Medium": 0.002, # 0.2%屏幕高度
        "Low": 0.0005    # 0.05%屏幕高度
    }
    
    print("比例阈值配置:")
    for lod, threshold in ratio_thresholds.items():
        print(f"{lod} LOD: >= {threshold:.4f} ({threshold*100:.2f}%屏幕高度)")
    
    print(f"\n{'距离(m)':<8} {'屏幕比例':<12} {'LOD级别':<10} {'说明':<20}")
    print(f"{'-'*55}")
    
    geometric_error = 0.5
    test_distances = [50, 100, 200, 500, 1000]
    
    for distance in test_distances:
        screen_pixels, screen_ratio = calculate_screen_metric(geometric_error, distance)
        lod = determine_lod_by_screen_metric(screen_pixels, screen_ratio, ratio_thresholds)
        
        print(f"{distance:<8} {screen_ratio:>10.4f} {lod:<10} {screen_ratio*100:.2f}%屏幕高度")

def test_edge_cases():
    """测试边界情况"""
    print(f"\n\n=== 边界情况测试 ===\n")
    
    thresholds = {"High": 2.0, "Medium": 8.0, "Low": 20.0}
    
    # 测试精确的阈值边界
    test_cases = [
        (1.9, "应该是Low"),
        (2.0, "应该是High"),
        (2.1, "应该是High"),
        (7.9, "应该是High"),
        (8.0, "应该是High"),
        (8.1, "应该是High"),
        (19.9, "应该是High"),
        (20.0, "应该是High"),
        (20.1, "应该是High"),
    ]
    
    print(f"{'屏幕误差(px)':<14} {'LOD级别':<10} {'预期':<12} {'状态':<6}")
    print(f"{'-'*50}")
    
    for screen_error, expected_desc in test_cases:
        screen_ratio = screen_error / 1080
        lod = determine_lod_by_screen_metric(screen_error, screen_ratio, thresholds)
        
        # 判断预期
        if "Low" in expected_desc:
            expected = "Low"
        else:
            expected = "High"  # 在当前逻辑下，>=2.0都是High
        
        status = "✅" if lod == expected else "❌"
        print(f"{screen_error:<14} {lod:<10} {expected_desc:<12} {status}")

if __name__ == "__main__":
    test_distance_vs_screen_error()
    test_lod_logic()
    test_threshold_units()
    test_edge_cases()
    
    print(f"\n\n=== 总结 ===")
    print("✅ 物理原理:")
    print("  - 距离越近 → 屏幕误差越大 → 需要高质量LOD")
    print("  - 距离越远 → 屏幕误差越小 → 低质量LOD即可")
    
    print(f"\n✅ LOD逻辑:")
    print("  - 屏幕误差 >= 阈值 → 使用对应质量LOD")
    print("  - 误差大时用高质量，误差小时用低质量")
    
    print(f"\n✅ 阈值设计:")
    print("  - High阈值应该最小（如2px）")
    print("  - Medium阈值中等（如8px）") 
    print("  - Low阈值最大（如20px）")
    print("  - 这样确保误差大时用高质量LOD")
