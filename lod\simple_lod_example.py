def _start_mainthread_update_subscription(update_interval: float = 1.0):
    """使用 Kit 的 update 事件在主线程上进行节流更新"""
    global realtime_update_active, realtime_update_data, auto_update_subscription

    # 防止重复订阅
    if auto_update_subscription:
        try:
            auto_update_subscription.unsubscribe()
        except Exception:
            pass
        auto_update_subscription = None

    app = omni.kit.app.get_app()
    stream = app.get_update_event_stream()

    import time
    last_ts = time.time()
    accumulator = 0.0

    def on_update(e):
        nonlocal last_ts, accumulator
        if not realtime_update_active or not realtime_update_data:
            return

        now = time.time()
        dt = now - last_ts
        last_ts = now
        accumulator += dt

        if accumulator < update_interval:
            return
        accumulator = 0.0

        try:
            stage = realtime_update_data['stage']
            region_bounds = realtime_update_data['region_bounds']
            usdz_paths = realtime_update_data['usdz_paths']

            target_lod, distance, screen_error = update_lod_visibility_by_geometric_error(
                stage, region_bounds, usdz_paths, verbose=False
            )
            # 可选：在此处按需打印/记录 target_lod、distance、screen_error
        except Exception as ex:
            print(f"Error in main-thread update: {ex}")

    auto_update_subscription = stream.create_subscription_to_pop(on_update, name="LODUpdateOnMainThread")
"""
简单LOD示例
只加载一个区域，根据相机距离加载不同的LOD级别
通过控制visibility实现LOD切换
"""

from pxr import Usd, UsdGeom, Gf, Sdf
import omni.usd
import math
import sys
sys.path.append("E:/wanleqi/isaacsim-python-scripts/lod")

from lod_scheduler import LODScheduler, BoundingBox, LODLevel, LODTile
import omni.kit.app
import omni.timeline
import asyncio
import omni.kit.commands

# 配置类
class Config:
    """LOD配置类"""
    def __init__(self):
        # 几何误差配置（世界单位，米）
        # 这些值表示每个LOD级别允许的最大几何误差，用于计算屏幕误差
        # 较小的几何误差对应更高质量的LOD
        self.lod_geometric_errors = {
            "High": 0.5,    # 高质量LOD：0.5米几何误差
            "Medium": 2.0,  # 中质量LOD：2.0米几何误差
            "Low": 8.0      # 低质量LOD：8.0米几何误差
        }

        # 屏幕误差阈值（像素或比例）
        # 支持两种单位：
        # - 像素阈值（>1，如 20/8/2）：按像素比较
        # - 比例阈值（<=1，如 0.02/0.008/0.002）：按屏幕高度比例比较
        # 注意：阈值表示"需要该LOD的最小误差"，误差大时用高质量LOD
        self.screen_error_thresholds = {
            "High": 20.0,   # 20像素 - 误差很大时用高质量
            "Medium": 8.0,  # 8像素 - 误差中等时用中质量
            "Low": 2.0      # 2像素 - 误差较小时用低质量
        }

        # 距离阈值（保持向后兼容）
        self.distance_threshold_high = 50.0
        self.distance_threshold_medium = 100.0
        self.distance_threshold_low = 200.0

# 全局变量来跟踪当前LOD状态
current_lod_state = {"last_lod": None, "last_distance": None}
# 全局变量来跟踪实时更新状态
realtime_update_active = False
realtime_update_data = None
# 全局变量来跟踪自动更新订阅
auto_update_subscription = None

def transform_point(p, transform_matrix):
    """
    变换点的函数，应用坐标系修正
    """
    point4 = Gf.Vec4d(p[0], p[1], p[2], 1.0)
    # 使用正确的Transform方法 - 使用矩阵乘法
    transformed = transform_matrix * point4
    return Gf.Vec3f(transformed[0], transformed[1], transformed[2])

def apply_coordinate_system_correction(minB, maxB, debug_info=False):
    """
    应用坐标系修正到边界框
    """
    if debug_info:
        print(f"  Original bounds: {minB} to {maxB}")
    
    # 应用 (90, 180, 0) 旋转的逆变换到坐标点
    # 这相当于将 NUREC 坐标系转换为 USD 坐标系
    
    # 创建旋转矩阵：先绕 Z 轴旋转 180°，再绕 X 轴旋转 90°
    # 注意：我们需要应用逆变换，所以是 (90, -180, 0) 的变换
    rot_z_180 = Gf.Matrix4d().SetRotate(Gf.Rotation(Gf.Vec3d(0, 0, 1), -180))
    rot_x_90 = Gf.Matrix4d().SetRotate(Gf.Rotation(Gf.Vec3d(1, 0, 0), 90))
    transform_matrix = rot_z_180 * rot_x_90

    # 重新计算变换后的最小最大值
    all_transformed_points = [
        transform_point(Gf.Vec3f(minB[0], minB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], minB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], maxB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(minB[0], maxB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(minB[0], minB[1], maxB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], minB[1], maxB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], maxB[1], maxB[2]), transform_matrix),
        transform_point(Gf.Vec3f(minB[0], maxB[1], maxB[2]), transform_matrix)
    ]

    # 计算变换后的实际边界
    min_x = min(p[0] for p in all_transformed_points)
    max_x = max(p[0] for p in all_transformed_points)
    min_y = min(p[1] for p in all_transformed_points)
    max_y = max(p[1] for p in all_transformed_points)
    min_z = min(p[2] for p in all_transformed_points)
    max_z = max(p[2] for p in all_transformed_points)

    corrected_minB = Gf.Vec3f(min_x, min_y, min_z)
    corrected_maxB = Gf.Vec3f(max_x, max_y, max_z)
    
    if debug_info:
        print(f"  Corrected bounds: {corrected_minB} to {corrected_maxB}")
    
    return corrected_minB, corrected_maxB

def get_nurec_bounds_from_stage():
    """从stage中获取NUREC prim的实际包围盒，应用坐标系修正"""
    stage = omni.usd.get_context().get_stage()
    
    print("Scanning stage for NUREC prims with bounds...")
    print("Applying coordinate system correction (90, 180, 0) rotation")
    
    min_point = Gf.Vec3f(float('inf'), float('inf'), float('inf'))
    max_point = Gf.Vec3f(float('-inf'), float('-inf'), float('-inf'))
    
    found_bounds = False
    nurec_prims = []
    
    for prim in stage.Traverse():
        # 查找有边界信息的prim
        attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
        attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")
        
        if attr_min and attr_max:
            try:
                min_bounds = Gf.Vec3f(*attr_min.Get())
                max_bounds = Gf.Vec3f(*attr_max.Get())
                
                # 应用坐标系修正
                corrected_min_bounds, corrected_max_bounds = apply_coordinate_system_correction(
                    min_bounds, max_bounds, debug_info=True
                )
                
                nurec_prims.append({
                    'path': str(prim.GetPath()),
                    'min_bounds': corrected_min_bounds,
                    'max_bounds': corrected_max_bounds,
                    'original_min_bounds': min_bounds,
                    'original_max_bounds': max_bounds
                })
                
                # 更新全局边界框（使用修正后的边界）
                min_point = Gf.Vec3f(
                    min(min_point[0], corrected_min_bounds[0]),
                    min(min_point[1], corrected_min_bounds[1]),
                    min(min_point[2], corrected_min_bounds[2])
                )
                max_point = Gf.Vec3f(
                    max(max_point[0], corrected_max_bounds[0]),
                    max(max_point[1], corrected_max_bounds[1]),
                    max(max_point[2], corrected_max_bounds[2])
                )
                found_bounds = True
                
                print(f"Found NUREC prim: {prim.GetPath()}")
                print(f"  Original bounds: {min_bounds} to {max_bounds}")
                print(f"  Corrected bounds: {corrected_min_bounds} to {corrected_max_bounds}")
                
            except Exception as e:
                print(f"Warning: Failed to read bounds for {prim.GetPath()}: {e}")
    
    if not found_bounds:
        print("Warning: No NUREC prims with bounds found in stage, using default bounds")
        min_point = Gf.Vec3f(-200, -200, -100)
        max_point = Gf.Vec3f(200, 200, 100)
        nurec_prims = []
    else:
        print(f"Found {len(nurec_prims)} NUREC prims with bounds")
        print(f"Global corrected bounds: {min_point} to {max_point}")
    
    return BoundingBox(min_point, max_point), nurec_prims

def load_usdz_file_into_stage(usdz_path, target_path):
    """将USDZ文件加载到stage中的指定路径"""
    try:
        stage = omni.usd.get_context().get_stage()
        
        # 使用USD的SdfLayer来引用USDZ文件
        layer = Sdf.Layer.FindOrOpen(usdz_path)
        if layer:
            # 创建引用
            prim = stage.DefinePrim(target_path)
            prim.GetReferences().AddReference(usdz_path)
            print(f"Successfully referenced USDZ file: {usdz_path} at {target_path}")
            return True
        else:
            print(f"Warning: Could not open USDZ file: {usdz_path}")
            return False
    except Exception as e:
        print(f"Error loading USDZ file {usdz_path}: {e}")
        return False

def create_single_region_scene_with_usdz(usdz_paths):
    """创建包含实际USDZ文件的单个区域场景，并为每个LOD写入基础属性"""
    stage = omni.usd.get_context().get_stage()
    
    print("Creating single region scene with actual USDZ files...")
    
    # 从stage中获取实际的NUREC包围盒
    region_bounds, nurec_prims = get_nurec_bounds_from_stage()
    
    # 创建区域容器
    region_path = "/World/SingleRegion"
    region_prim = stage.DefinePrim(region_path)
    region_prim.SetTypeName("Xform")
    
    # 计算区域中心点
    center = region_bounds.center
    print(f"Region center: {center}")
    
    # 创建3个LOD级别并加载USDZ文件
    lod_levels = [
        {
            "name": "High",
            "usdz_path": usdz_paths["High"],
            "position": center,
            "visible": True  # 默认显示高质量
        },
        {
            "name": "Medium", 
            "usdz_path": usdz_paths["Medium"],
            "position": center,
            "visible": False
        },
        {
            "name": "Low",
            "usdz_path": usdz_paths["Low"],
            "position": center,
            "visible": False
        }
    ]
    
    for lod_info in lod_levels:
        # 检查是否已存在该LOD层级
        lod_path = f"{region_path}/LOD_{lod_info['name']}"
        existing_lod_prim = stage.GetPrimAtPath(lod_path)
        
        if existing_lod_prim and existing_lod_prim.IsValid():
            print(f"LOD_{lod_info['name']} already exists at {lod_path}, skipping creation")
            continue
        
        # 创建LOD层级容器
        lod_prim = stage.DefinePrim(lod_path)
        lod_prim.SetTypeName("Xform")
        
        # 设置位置 - 修复：检查是否已存在transform操作
        xformable = UsdGeom.Xformable(lod_prim)
        
        # 检查是否已经有translate操作
        existing_ops = xformable.GetOrderedXformOps()
        has_translate = any(op.GetOpType() == UsdGeom.XformOp.TypeTranslate for op in existing_ops)
        
        if not has_translate:
            # 只有在没有translate操作时才添加
            xformable.AddTranslateOp().Set(lod_info['position'])
        else:
            # 如果已存在，则更新现有的translate操作
            for op in existing_ops:
                if op.GetOpType() == UsdGeom.XformOp.TypeTranslate:
                    op.Set(lod_info['position'])
                    break
        
        # 设置边界属性与LOD级别（使用修正后的坐标系）
        lod_prim.CreateAttribute("omni:nurec:crop:minBounds", Sdf.ValueTypeNames.Float3).Set(region_bounds.min_point)
        lod_prim.CreateAttribute("omni:nurec:crop:maxBounds", Sdf.ValueTypeNames.Float3).Set(region_bounds.max_point)
        lod_prim.CreateAttribute("omni:nurec:lod:level", Sdf.ValueTypeNames.String).Set(lod_info['name'])
        # 写入几何误差和屏幕误差阈值
        try:
            # 几何误差（世界单位）
            geometric_error = {"High": 0.5, "Medium": 2.0, "Low": 8.0}[lod_info['name']]
            lod_prim.CreateAttribute("omni:nurec:lod:geometric_error", Sdf.ValueTypeNames.Float).Set(geometric_error)

            # 屏幕误差阈值
            screen_threshold = {"High": 2.0, "Medium": 8.0, "Low": 20.0}[lod_info['name']]
            lod_prim.CreateAttribute("omni:nurec:lod:sse", Sdf.ValueTypeNames.Float).Set(screen_threshold)

        except Exception:
            pass
        
        
        # 加载USDZ文件
        usdz_loaded = load_usdz_file_into_stage(lod_info['usdz_path'], f"{lod_path}/USDZContent")
        
        if usdz_loaded:
            # 使用UsdGeom.Imageable设置初始可见性
            imageable = UsdGeom.Imageable(lod_prim)
            vis_attr = imageable.GetVisibilityAttr()
            vis_attr.Set(UsdGeom.Tokens.inherited if lod_info['visible'] else UsdGeom.Tokens.invisible)
            
            print(f"Created LOD_{lod_info['name']} with loaded USDZ at {lod_path}")
            print(f"  Corrected bounds: {region_bounds.min_point} to {region_bounds.max_point}")
        else:
            print(f"Failed to load USDZ for LOD_{lod_info['name']} at {lod_path}")
        
        print(f"  USDZ Path: {lod_info['usdz_path']}")
        print(f"  Position: {lod_info['position']}")
        print(f"  Visible: {lod_info['visible']}")
    
    # 创建相机
    camera_path = "/World/Camera"
    if not stage.GetPrimAtPath(camera_path):
        camera = UsdGeom.Camera.Define(stage, camera_path)
        camera_position = Gf.Vec3f(center[0], center[1], center[2] + region_bounds.size[2] * 2)
        
        # 设置相机位置 - 修复：检查是否已存在transform操作
        xformable = UsdGeom.Xformable(camera)
        existing_ops = xformable.GetOrderedXformOps()
        has_translate = any(op.GetOpType() == UsdGeom.XformOp.TypeTranslate for op in existing_ops)
        
        if not has_translate:
            xformable.AddTranslateOp().Set(camera_position)
        else:
            for op in existing_ops:
                if op.GetOpType() == UsdGeom.XformOp.TypeTranslate:
                    op.Set(camera_position)
                    break
        
        print(f"Created camera at {camera_path}")
        print(f"Camera position: {camera_position}")
    else:
        # 如果相机已存在，更新其位置
        camera = stage.GetPrimAtPath(camera_path)
        camera_position = Gf.Vec3f(center[0], center[1], center[2] + region_bounds.size[2] * 2)
        
        xformable = UsdGeom.Xformable(camera)
        existing_ops = xformable.GetOrderedXformOps()
        has_translate = any(op.GetOpType() == UsdGeom.XformOp.TypeTranslate for op in existing_ops)
        
        if not has_translate:
            xformable.AddTranslateOp().Set(camera_position)
        else:
            for op in existing_ops:
                if op.GetOpType() == UsdGeom.XformOp.TypeTranslate:
                    op.Set(camera_position)
                    break
        
        print(f"Updated existing camera at {camera_path}")
        print(f"Camera position: {camera_position}")
    
    print("Single region scene with USDZ files created successfully!")
    print(f"Region bounds: {region_bounds.min_point} to {region_bounds.max_point}")
    print(f"Region size: {region_bounds.size}")
    print(f"Region center: {center}")
    
    return stage, region_bounds

def create_single_region_lod_tiles(region_bounds, usdz_paths):
    """为单个区域创建LOD瓦片，基于实际边界调整距离阈值"""
    print("\n=== Creating Single Region LOD Tiles ===")
    
    # 根据区域大小动态调整距离阈值
    region_size = region_bounds.size
    max_dimension = max(region_size[0], region_size[1], region_size[2])
    
    # 基于区域大小计算合适的距离阈值
    high_threshold = max_dimension * 0.5    # 近距离
    medium_threshold = max_dimension * 1.0   # 中距离
    low_threshold = max_dimension * 2.0      # 远距离
    
    print(f"Region max dimension: {max_dimension}")
    print(f"Distance thresholds - High: {high_threshold:.1f}, Medium: {medium_threshold:.1f}, Low: {low_threshold:.1f}")
    
    # 创建3个LOD瓦片，对应同一个区域的不同质量级别
    lod_tiles = [
        LODTile(
            id="/World/SingleRegion/LOD_High",
            bounding_box=region_bounds,
            lod_level=LODLevel.HIGH,
            usdz_path=usdz_paths["High"],
            screen_error=2.0,           # 近距离显示
            distance_threshold=high_threshold
        ),
        LODTile(
            id="/World/SingleRegion/LOD_Medium", 
            bounding_box=region_bounds,
            lod_level=LODLevel.MEDIUM,
            usdz_path=usdz_paths["Medium"],
            screen_error=8.0,           # 中距离显示
            distance_threshold=medium_threshold
        ),
        LODTile(
            id="/World/SingleRegion/LOD_Low",
            bounding_box=region_bounds,
            lod_level=LODLevel.LOW,
            usdz_path=usdz_paths["Low"],
            screen_error=20.0,          # 远距离显示
            distance_threshold=low_threshold
        )
    ]
    
    for tile in lod_tiles:
        print(f"Created LOD tile: {tile.id}")
        print(f"  LOD Level: {tile.lod_level.value}")
        print(f"  Screen Error: {tile.screen_error}")
        print(f"  Distance Threshold: {tile.distance_threshold}")
        print(f"  USDZ Path: {tile.usdz_path}")
    
    return lod_tiles

def calculate_screen_metric(geometric_error, distance, fov=60.0, screen_height=1080):
    """
    计算几何误差投影到屏幕的度量值

    Args:
        geometric_error: 几何误差（世界单位，米）
        distance: 到目标的距离（米）
        fov: 视场角（度）
        screen_height: 屏幕高度（像素）

    Returns:
        tuple: (screen_pixels, screen_ratio)
            - screen_pixels: 屏幕误差（像素）
            - screen_ratio: 屏幕误差占屏幕高度的比例
    """
    if distance <= 0:
        return float('inf'), float('inf')

    # 计算焦距（像素单位）
    focal_len_px = (screen_height / 2) / math.tan(math.radians(fov / 2))

    # 几何误差投影到屏幕的像素误差
    screen_pixels = (geometric_error / distance) * focal_len_px

    # 计算屏幕比例
    screen_ratio = screen_pixels / screen_height

    return screen_pixels, screen_ratio

def determine_lod_by_screen_metric(screen_pixels, screen_ratio, thresholds, verbose=False):
    """
    根据屏幕度量值确定LOD级别

    Args:
        screen_pixels: 屏幕误差（像素）
        screen_ratio: 屏幕误差占屏幕高度的比例
        thresholds: LOD阈值字典，格式：{"High": threshold, "Medium": threshold, "Low": threshold}
        verbose: 是否输出详细信息

    Returns:
        str: LOD级别 ("High", "Medium", "Low")
    """
    # 自动判断阈值单位（<=1 则按比例，否则按像素）
    def _get_metric_value(threshold_value):
        if threshold_value <= 1.0:
            # 比例阈值
            return screen_ratio
        else:
            # 像素阈值
            return screen_pixels

    # 获取各级别阈值
    high_threshold = thresholds.get("High", 2.0)
    medium_threshold = thresholds.get("Medium", 8.0)
    low_threshold = thresholds.get("Low", 20.0)

    # 获取对应的度量值（使用High LOD的单位类型）
    metric_value = _get_metric_value(high_threshold)

    # 正确的LOD判断逻辑：屏幕误差大于等于阈值时使用该LOD
    # 屏幕误差大 → 需要高质量LOD
    if metric_value >= high_threshold:
        return "High"
    elif metric_value >= medium_threshold:
        return "Medium"
    elif metric_value >= low_threshold:
        return "Low"
    else:
        # 屏幕误差很小，使用最低质量即可
        return "Low"

def update_lod_visibility_by_geometric_error(stage, region_bounds, usdz_paths, verbose=True, config=None):
    """根据几何误差和屏幕投影确定LOD级别"""
    if verbose:
        print("\n=== Updating LOD Visibility by Geometric Error ===")

    # 获取相机位置
    camera = stage.GetPrimAtPath("/World/Camera")
    if not camera:
        if verbose:
            print("Camera not found!")
        return

    # 获取相机位置
    xformable = UsdGeom.Xformable(camera)
    transform = xformable.GetLocalTransformation()
    camera_position = Gf.Vec3f(transform.ExtractTranslation())

    # 计算到区域中心的距离
    center = region_bounds.center
    distance = math.sqrt(
        (camera_position[0] - center[0])**2 +
        (camera_position[1] - center[1])**2 +
        (camera_position[2] - center[2])**2
    )

    # 获取几何误差配置
    def _get_geometric_error(lod_name: str) -> float:
        """获取LOD的几何误差（世界单位）"""
        if config and hasattr(config, 'lod_geometric_errors'):
            return config.lod_geometric_errors.get(lod_name, {"High": 0.5, "Medium": 2.0, "Low": 8.0}[lod_name])
        return {"High": 0.5, "Medium": 2.0, "Low": 8.0}[lod_name]

    def _get_screen_threshold(lod_name: str) -> float:
        """获取LOD的屏幕误差阈值"""
        if config and hasattr(config, 'screen_error_thresholds'):
            return config.screen_error_thresholds.get(lod_name, {"High": 2.0, "Medium": 8.0, "Low": 20.0}[lod_name])
        return {"High": 2.0, "Medium": 8.0, "Low": 20.0}[lod_name]

    # 计算每个LOD级别的屏幕度量值
    fov = 60.0
    screen_height = 1080

    lod_metrics = {}
    for lod_name in ["High", "Medium", "Low"]:
        geometric_error = _get_geometric_error(lod_name)
        screen_pixels, screen_ratio = calculate_screen_metric(geometric_error, distance, fov, screen_height)
        lod_metrics[lod_name] = {
            'geometric_error': geometric_error,
            'screen_pixels': screen_pixels,
            'screen_ratio': screen_ratio,
            'threshold': _get_screen_threshold(lod_name)
        }

    # 获取屏幕误差阈值
    thresholds = {lod: lod_metrics[lod]['threshold'] for lod in ["High", "Medium", "Low"]}

    # 使用High LOD的屏幕度量值来确定LOD级别
    high_metrics = lod_metrics["High"]
    target_lod = determine_lod_by_screen_metric(
        high_metrics['screen_pixels'],
        high_metrics['screen_ratio'],
        thresholds
    )

    # 距离回退机制（保持向后兼容）
    if config:
        d_high = float(getattr(config, 'distance_threshold_high', 50.0))
        d_med = float(getattr(config, 'distance_threshold_medium', 100.0))
    else:
        d_high, d_med = 50.0, 100.0

    # 距离等级判断
    if distance <= d_high:
        distance_lod = "High"
    elif distance <= d_med:
        distance_lod = "Medium"
    else:
        distance_lod = "Low"

    # 取更高质量的LOD（距离和屏幕误差的最大值）
    lod_priority = {"Low": 0, "Medium": 1, "High": 2}
    final_lod_priority = max(lod_priority[target_lod], lod_priority[distance_lod])
    final_lod = ["Low", "Medium", "High"][final_lod_priority]

    if verbose:
        print(f"Camera position: {camera_position}")
        print(f"Distance to region center: {distance:.1f}")
        print(f"LOD metrics:")
        for lod_name in ["High", "Medium", "Low"]:
            metrics = lod_metrics[lod_name]
            threshold = metrics['threshold']
            unit = "ratio" if threshold <= 1.0 else "px"
            print(f"  {lod_name}: geo_error={metrics['geometric_error']:.1f}m, "
                  f"screen={metrics['screen_pixels']:.1f}px ({metrics['screen_ratio']:.4f}ratio), "
                  f"threshold={threshold}{unit}")
        print(f"Screen-based LOD: {target_lod}")
        print(f"Distance-based LOD: {distance_lod}")
        print(f"Final LOD level: {final_lod}")

    # 更新所有LOD级别的可见性
    lod_levels = ["High", "Medium", "Low"]
    for lod_name in lod_levels:
        lod_path = f"/World/SingleRegion/LOD_{lod_name}"
        lod_prim = stage.GetPrimAtPath(lod_path)

        if lod_prim:
            # 使用UsdGeom.Imageable设置可见性
            imageable = UsdGeom.Imageable(lod_prim)
            vis_attr = imageable.GetVisibilityAttr()

            # 设置可见性：只有目标LOD可见，其他都隐藏
            visible = (lod_name == final_lod)
            vis_attr.Set(UsdGeom.Tokens.inherited if visible else UsdGeom.Tokens.invisible)

            if verbose:
                print(f"  LOD_{lod_name}: {'Visible' if visible else 'Hidden'}")
        else:
            if verbose:
                print(f"  LOD_{lod_name}: Not found")

    # 返回用于日志/状态的结果
    return final_lod, distance, lod_metrics["High"]["screen_pixels"]

def update_lod_visibility_by_distance(stage, region_bounds, usdz_paths, verbose=True):
    """根据屏幕误差+距离联合判定LOD；优先读取每LOD的 omni:nurec:lod:sse 阈值。
    返回 (target_lod, distance, screen_error) 供外部复用，避免重复计算。"""
    if verbose:
        print("\n=== Updating LOD Visibility by Distance ===")
    
    # 获取相机位置
    camera = stage.GetPrimAtPath("/World/Camera")
    if not camera:
        if verbose:
            print("Camera not found!")
        return
    
    # 获取相机位置
    xformable = UsdGeom.Xformable(camera)
    transform = xformable.GetLocalTransformation()
    camera_position = Gf.Vec3f(transform.ExtractTranslation())
    
    # 计算到区域中心的距离
    center = region_bounds.center
    distance = math.sqrt(
        (camera_position[0] - center[0])**2 + 
        (camera_position[1] - center[1])**2 + 
        (camera_position[2] - center[2])**2
    )
    
    # 计算屏幕误差
    region_size = region_bounds.size
    max_dimension = max(region_size[0], region_size[1], region_size[2])
    fov = 60.0
    screen_height = 1080
    focal_len_px = (screen_height / 2) / math.tan(math.radians(fov / 2))
    if distance <= 0:
        pixel_size = float('inf')
    else:
        pixel_size = (max_dimension / distance) * focal_len_px
    screen_ratio = pixel_size / screen_height if screen_height > 0 else float('inf')

    # 读取每LOD的 SSE 阈值（来自 prim 属性）；距离阈值按区域尺寸简化为 0.5x / 1.0x
    def _get_lod_sse(lod_name: str) -> float:
        try:
            lod_path = f"/World/SingleRegion/LOD_{lod_name}"
            lod_prim = stage.GetPrimAtPath(lod_path)
            if lod_prim:
                sse_attr = lod_prim.GetAttribute("omni:nurec:lod:sse")
                if sse_attr and sse_attr.HasValue():
                    return float(sse_attr.Get())
        except Exception:
            pass
        return {"High": 2.0, "Medium": 8.0, "Low": 20.0}.get(lod_name, 20.0)

    sse_high = _get_lod_sse("High")
    sse_med = _get_lod_sse("Medium")
    use_ratio = (sse_high <= 1.0 and sse_med <= 1.0)

    d_high = max_dimension * 0.5
    d_med = max_dimension * 1.0

    # 各自映射等级，再取高者
    screen_metric = screen_ratio if use_ratio else pixel_size
    if screen_metric >= sse_high:
        rank_screen = 2
    elif screen_metric >= sse_med:
        rank_screen = 1
    else:
        rank_screen = 0

    if distance <= d_high:
        rank_distance = 2
    elif distance <= d_med:
        rank_distance = 1
    else:
        rank_distance = 0

    target_rank = max(rank_screen, rank_distance)
    target_lod = ["Low", "Medium", "High"][target_rank]

    if verbose:
        print(f"Camera position: {camera_position}")
        print(f"Distance to region center: {distance:.1f}")
        print(f"Screen metric: {screen_metric:.4f} ({'ratio' if use_ratio else 'px'}), pixel_size: {pixel_size:.2f}, ratio: {screen_ratio:.4f}")
    
    if verbose:
        print(f"Target LOD level: {target_lod}")
    
    # 更新所有LOD级别的可见性（同步对子内容设置，避免继承被覆盖）
    lod_levels = ["High", "Medium", "Low"]
    for lod_name in lod_levels:
        lod_path = f"/World/SingleRegion/LOD_{lod_name}"
        lod_prim = stage.GetPrimAtPath(lod_path)
        
        if lod_prim:
            # 使用UsdGeom.Imageable设置可见性
            imageable = UsdGeom.Imageable(lod_prim)
            vis_attr = imageable.GetVisibilityAttr()
            
            # 设置可见性：只有目标LOD可见，其他都隐藏
            visible = (lod_name == target_lod)
            vis_attr.Set(UsdGeom.Tokens.inherited if visible else UsdGeom.Tokens.invisible)
            
            if verbose:
                print(f"  LOD_{lod_name}: {'Visible' if visible else 'Hidden'}")
        else:
            if verbose:
                print(f"  LOD_{lod_name}: Not found")

def test_distance_based_lod():
    """测试基于距离的LOD切换"""
    print("\n=== Testing Distance-Based LOD Switching ===")
    
    # 配置USDZ文件路径（请根据实际情况修改）
    usdz_paths = {
        "High": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/20.usdz",
        "Medium": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/18.usdz", 
        "Low": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/17.usdz"
    }
    
    # 创建场景和LOD瓦片
    stage, region_bounds = create_single_region_scene_with_usdz(usdz_paths)
    lod_tiles = create_single_region_lod_tiles(region_bounds, usdz_paths)
    
    # 创建LOD调度器
    scheduler = LODScheduler(stage, camera_path="/World/Camera")
    
    # 添加LOD瓦片
    for tile in lod_tiles:
        scheduler.lod_tiles[tile.id] = tile
    
    # 构建八叉树
    print("\nBuilding octree...")
    scheduler.build_octree_from_stage(max_depth=4, min_area_threshold=100.0)
    
    # 获取区域中心点
    center = region_bounds.center
    max_dimension = max(region_bounds.size[0], region_bounds.size[1], region_bounds.size[2])
    
    # 根据区域大小动态生成测试距离
    test_distances = [
        {"name": "Very Close", "pos": Gf.Vec3f(center[0], center[1], center[2] + max_dimension * 0.2), "expected_lod": "High"},
        {"name": "Close", "pos": Gf.Vec3f(center[0], center[1], center[2] + max_dimension * 0.5), "expected_lod": "High"},
        {"name": "Medium", "pos": Gf.Vec3f(center[0], center[1], center[2] + max_dimension * 1.0), "expected_lod": "Medium"},
        {"name": "Far", "pos": Gf.Vec3f(center[0], center[1], center[2] + max_dimension * 1.5), "expected_lod": "Low"},
        {"name": "Very Far", "pos": Gf.Vec3f(center[0], center[1], center[2] + max_dimension * 2.5), "expected_lod": "Low"}
    ]
    
    for test in test_distances:
        print(f"\n--- Testing {test['name']} ---")
        print(f"Expected LOD: {test['expected_lod']}")
        
        # 更新相机位置
        camera = stage.GetPrimAtPath("/World/Camera")
        if camera:
            xformable = UsdGeom.Xformable(camera)
            existing_ops = xformable.GetOrderedXformOps()
            has_translate = any(op.GetOpType() == UsdGeom.XformOp.TypeTranslate for op in existing_ops)
            
            if not has_translate:
                xformable.AddTranslateOp().Set(test['pos'])
            else:
                for op in existing_ops:
                    if op.GetOpType() == UsdGeom.XformOp.TypeTranslate:
                        op.Set(test['pos'])
                        break
        
        # 更新LOD可见性
        update_lod_visibility_by_geometric_error(stage, region_bounds, usdz_paths)
        
        # 获取当前相机位置和计算的距离
        current_pos = scheduler.get_camera_position()
        if current_pos:
            distance = scheduler.calculate_distance_to_camera(region_bounds, current_pos)
            print(f"Camera position: {current_pos}")
            print(f"Distance to region center: {distance:.1f}")
            
            # 计算屏幕误差
            screen_error = scheduler.calculate_screen_error(region_bounds, current_pos, fov=60.0, screen_height=1080)
            print(f"Screen error: {screen_error:.1f} pixels")
            
            # 确定应该显示的LOD级别
            determined_lod = scheduler.determine_lod_level(screen_error, distance)
            print(f"Determined LOD level: {determined_lod.value}")
            
            # 检查是否符合预期
            if determined_lod.value.upper() == test['expected_lod'].upper():
                print("✅ LOD level matches expectation")
            else:
                print("❌ LOD level does not match expectation")

def simple_interactive_demo():
    """简单的交互式演示"""
    print("\n=== Simple Interactive Demo ===")
    
    # 配置USDZ文件路径（请根据实际情况修改）
    usdz_paths = {
        "High": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/20.usdz",
        "Medium": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/18.usdz", 
        "Low": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/17.usdz"
    }
    
    # 创建场景和LOD瓦片
    stage, region_bounds = create_single_region_scene_with_usdz(usdz_paths)
    lod_tiles = create_single_region_lod_tiles(region_bounds, usdz_paths)
    
    # 创建LOD调度器
    scheduler = LODScheduler(stage, camera_path="/World/Camera")
    
    # 添加LOD瓦片
    for tile in lod_tiles:
        scheduler.lod_tiles[tile.id] = tile
    
    # 构建八叉树
    scheduler.build_octree_from_stage(max_depth=4, min_area_threshold=100.0)
    
    # 显示找到的LOD瓦片信息
    print(f"\nFound {len(scheduler.lod_tiles)} LOD tiles:")
    for tile_id, tile in scheduler.lod_tiles.items():
        print(f"  - {tile_id}: {tile.lod_level.value}")
    
    # 获取区域中心点和大小
    center = region_bounds.center
    max_dimension = max(region_bounds.size[0], region_bounds.size[1], region_bounds.size[2])
    
    print("Interactive demo ready!")
    print("You can now move the camera in Omniverse to see LOD changes.")
    print("The system will automatically switch between High, Medium, and Low LOD based on distance.")
    print(f"\nRegion center: {center}")
    print(f"Region max dimension: {max_dimension}")
    print("\nLOD switching distances:")
    print(f"- High LOD: 0-{max_dimension * 0.5:.1f} units")
    print(f"- Medium LOD: {max_dimension * 0.5:.1f}-{max_dimension * 1.0:.1f} units") 
    print(f"- Low LOD: {max_dimension * 1.0:.1f}+ units")
    
    # 模拟一些相机移动，基于实际区域大小
    demo_positions = [
        (center[0], center[1], center[2] + max_dimension * 0.2, "Very Close - Should show High LOD"),
        # (center[0], center[1], center[2] + max_dimension * 0.6, "Close - Should show High LOD"),
        # (center[0], center[1], center[2] + max_dimension * 1.2, "Medium - Should show Medium LOD"),
        # (center[0], center[1], center[2] + max_dimension * 1.8, "Far - Should show Low LOD"),
        # (center[0], center[1], center[2] + max_dimension * 3.0, "Very Far - Should show Low LOD")
    ]
    
    for x, y, z, description in demo_positions:
        print(f"\n--- {description} ---")
        
        # 更新相机位置
        camera = stage.GetPrimAtPath("/World/Camera")
        if camera:
            xformable = UsdGeom.Xformable(camera)
            existing_ops = xformable.GetOrderedXformOps()
            has_translate = any(op.GetOpType() == UsdGeom.XformOp.TypeTranslate for op in existing_ops)
            
            if not has_translate:
                xformable.AddTranslateOp().Set(Gf.Vec3f(x, y, z))
            else:
                for op in existing_ops:
                    if op.GetOpType() == UsdGeom.XformOp.TypeTranslate:
                        op.Set(Gf.Vec3f(x, y, z))
                        break
        
        # 更新LOD可见性
        update_lod_visibility_by_geometric_error(stage, region_bounds, usdz_paths)
        
        # 显示当前状态
        current_pos = scheduler.get_camera_position()
        if current_pos:
            distance = scheduler.calculate_distance_to_camera(region_bounds, current_pos)
            screen_error = scheduler.calculate_screen_error(region_bounds, current_pos, fov=60.0, screen_height=1080)
            determined_lod = scheduler.determine_lod_level(screen_error, distance)
            
            print(f"Camera: {current_pos}, Distance: {distance:.1f}, Screen Error: {screen_error:.1f}, LOD: {determined_lod.value}")

def start_automatic_lod_switching():
    """启动真正的自动LOD切换，使用Omniverse事件系统"""
    global realtime_update_active, realtime_update_data, auto_update_subscription
    
    print("\n=== Starting Automatic LOD Switching ===")
    
    # 配置USDZ文件路径（请根据实际情况修改）
    usdz_paths = {
        "High": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/20.usdz",
        "Medium": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/18.usdz", 
        "Low": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/17.usdz"
    }
    
    # 创建场景
    stage, region_bounds = create_single_region_scene_with_usdz(usdz_paths)
    
    # 保存数据到全局变量
    realtime_update_data = {
        'stage': stage,
        'region_bounds': region_bounds,
        'usdz_paths': usdz_paths
    }
    realtime_update_active = True
    
    # 方法1：尝试使用USD事件系统
    try:
        print("Attempting to use USD event system for automatic updates...")
        auto_update_subscription = omni.usd.get_context().get_stage_event_stream().create_subscription_to_pop_by_type(
            int(omni.usd.StageEventType.CAMERA_CHANGED), 
            _on_camera_changed
        )
        print("✅ Automatic LOD switching started using USD event system!")
        print("The system will automatically update LOD when camera changes.")
        return stage, region_bounds, None
    except Exception as e:
        print(f"USD event system failed: {e}")
    
    # 方法2：尝试使用时间线事件
    try:
        print("Attempting to use timeline events for automatic updates...")
        timeline = omni.timeline.get_timeline_interface()
        if hasattr(timeline, 'subscribe_to_frame_events'):
            auto_update_subscription = timeline.subscribe_to_frame_events(_on_frame_update)
            print("✅ Automatic LOD switching started using timeline events!")
            print("The system will automatically update LOD every frame.")
            return stage, region_bounds, None
    except Exception as e:
        print(f"Timeline events failed: {e}")
    
    # 方法3：使用主线程事件节流（更稳定，避免后台线程操作USD）
    try:
        print("Attempting to use main-thread throttled updates...")
        _start_mainthread_update_subscription(update_interval=1.0)
        print("✅ Automatic LOD switching started using main-thread update subscription!")
        print("The system will automatically update LOD on the main thread.")
        return stage, region_bounds, None
    except Exception as e:
        print(f"Main-thread throttled updates failed: {e}")
    
    # 方法4：使用异步循环
    try:
        print("Attempting to use async loop for automatic updates...")
        asyncio.create_task(_async_update_loop())
        print("✅ Automatic LOD switching started using async loop!")
        print("The system will automatically update LOD continuously.")
        return stage, region_bounds, None
    except Exception as e:
        print(f"Async loop failed: {e}")
    
    # 如果所有方法都失败，回退到手动模式
    print("⚠️ All automatic methods failed, falling back to manual mode")
    print("You can use manual_update() to update LOD manually")
    return stage, region_bounds, None

def _on_camera_changed(event):
    """USD相机变化事件回调（触发一次更新）"""
    global realtime_update_active, realtime_update_data, current_lod_state
    
    if not realtime_update_active or not realtime_update_data:
        return
    
    try:
        stage = realtime_update_data['stage']
        region_bounds = realtime_update_data['region_bounds']
        usdz_paths = realtime_update_data['usdz_paths']
        
        # 更新LOD可见性
        update_lod_visibility_by_geometric_error(stage, region_bounds, usdz_paths, verbose=False)
        
        # 获取当前相机位置和LOD状态
        camera = stage.GetPrimAtPath("/World/Camera")
        if camera:
            xformable = UsdGeom.Xformable(camera)
            transform = xformable.GetLocalTransformation()
            camera_position = Gf.Vec3f(transform.ExtractTranslation())
            
            center = region_bounds.center
            distance = math.sqrt(
                (camera_position[0] - center[0])**2 + 
                (camera_position[1] - center[1])**2 + 
                (camera_position[2] - center[2])**2
            )
            
            # 确定当前LOD级别
            region_size = region_bounds.size
            max_dimension = max(region_size[0], region_size[1], region_size[2])
            
            high_threshold = max_dimension * 0.5
            medium_threshold = max_dimension * 1.0
            
            if distance <= high_threshold:
                current_lod = "High"
            elif distance <= medium_threshold:
                current_lod = "Medium"
            else:
                current_lod = "Low"
            
            # 只在LOD状态改变时输出信息
            if (current_lod_state["last_lod"] != current_lod or 
                abs(current_lod_state["last_distance"] - distance) > 10.0):
                
                print(f"🔄 LOD switched to {current_lod} (distance: {distance:.1f})")
                current_lod_state["last_lod"] = current_lod
                current_lod_state["last_distance"] = distance
                
    except Exception as e:
        print(f"Error in camera change callback: {e}")

def _on_frame_update():
    """帧更新回调（保留）"""
    global realtime_update_active, realtime_update_data, current_lod_state
    
    if not realtime_update_active or not realtime_update_data:
        return
    
    try:
        stage = realtime_update_data['stage']
        region_bounds = realtime_update_data['region_bounds']
        usdz_paths = realtime_update_data['usdz_paths']
        
        # 更新LOD可见性
        update_lod_visibility_by_geometric_error(stage, region_bounds, usdz_paths, verbose=False)
        
        # 获取当前相机位置和LOD状态
        camera = stage.GetPrimAtPath("/World/Camera")
        if camera:
            xformable = UsdGeom.Xformable(camera)
            transform = xformable.GetLocalTransformation()
            camera_position = Gf.Vec3f(transform.ExtractTranslation())
            
            center = region_bounds.center
            distance = math.sqrt(
                (camera_position[0] - center[0])**2 + 
                (camera_position[1] - center[1])**2 + 
                (camera_position[2] - center[2])**2
            )
            
            # 确定当前LOD级别
            region_size = region_bounds.size
            max_dimension = max(region_size[0], region_size[1], region_size[2])
            
            high_threshold = max_dimension * 0.5
            medium_threshold = max_dimension * 1.0
            
            if distance <= high_threshold:
                current_lod = "High"
            elif distance <= medium_threshold:
                current_lod = "Medium"
            else:
                current_lod = "Low"
            
            # 只在LOD状态改变时输出信息
            if (current_lod_state["last_lod"] != current_lod or 
                abs(current_lod_state["last_distance"] - distance) > 10.0):
                
                print(f"🔄 LOD switched to {current_lod} (distance: {distance:.1f})")
                current_lod_state["last_lod"] = current_lod
                current_lod_state["last_distance"] = distance
                
    except Exception as e:
        print(f"Error in frame update callback: {e}")

def _start_timer_based_updates():
    """启动基于定时器的更新（不再使用：保留占位）"""
    global realtime_update_active, realtime_update_data, current_lod_state
    
    def timer_update():
        if not realtime_update_active or not realtime_update_data:
            return
        
        try:
            stage = realtime_update_data['stage']
            region_bounds = realtime_update_data['region_bounds']
            usdz_paths = realtime_update_data['usdz_paths']
            
            # 更新LOD可见性
            update_lod_visibility_by_geometric_error(stage, region_bounds, usdz_paths, verbose=False)
            
            # 获取当前相机位置和LOD状态
            camera = stage.GetPrimAtPath("/World/Camera")
            if camera:
                xformable = UsdGeom.Xformable(camera)
                transform = xformable.GetLocalTransformation()
                camera_position = Gf.Vec3f(transform.ExtractTranslation())
                
                center = region_bounds.center
                distance = math.sqrt(
                    (camera_position[0] - center[0])**2 + 
                    (camera_position[1] - center[1])**2 + 
                    (camera_position[2] - center[2])**2
                )
                
                # 确定当前LOD级别
                region_size = region_bounds.size
                max_dimension = max(region_size[0], region_size[1], region_size[2])
                
                high_threshold = max_dimension * 0.5
                medium_threshold = max_dimension * 1.0
                
                if distance <= high_threshold:
                    current_lod = "High"
                elif distance <= medium_threshold:
                    current_lod = "Medium"
                else:
                    current_lod = "Low"
                
                # 只在LOD状态改变时输出信息
                if (current_lod_state["last_lod"] != current_lod or 
                    abs(current_lod_state["last_distance"] - distance) > 10.0):
                    
                    print(f"🔄 LOD switched to {current_lod} (distance: {distance:.1f})")
                    current_lod_state["last_lod"] = current_lod
                    current_lod_state["last_distance"] = distance
                    
        except Exception as e:
            print(f"Error in timer update: {e}")
        
        # 继续定时器
        if realtime_update_active:
            omni.kit.app.get_app().get_update_event_stream().create_subscription_to_pop_by_type(
                int(omni.kit.app.UpdateEventType.DEFAULT), 
                lambda e: timer_update()
            )
    
    # 已废弃
    return

async def _async_update_loop():
    """异步更新循环（保留）"""
    global realtime_update_active, realtime_update_data, current_lod_state
    
    while realtime_update_active and realtime_update_data:
        try:
            stage = realtime_update_data['stage']
            region_bounds = realtime_update_data['region_bounds']
            usdz_paths = realtime_update_data['usdz_paths']
            
            # 更新LOD可见性
            update_lod_visibility_by_geometric_error(stage, region_bounds, usdz_paths, verbose=False)
            
            # 获取当前相机位置和LOD状态
            camera = stage.GetPrimAtPath("/World/Camera")
            if camera:
                xformable = UsdGeom.Xformable(camera)
                transform = xformable.GetLocalTransformation()
                camera_position = Gf.Vec3f(transform.ExtractTranslation())
                
                center = region_bounds.center
                distance = math.sqrt(
                    (camera_position[0] - center[0])**2 + 
                    (camera_position[1] - center[1])**2 + 
                    (camera_position[2] - center[2])**2
                )
                
                # 确定当前LOD级别
                region_size = region_bounds.size
                max_dimension = max(region_size[0], region_size[1], region_size[2])
                
                high_threshold = max_dimension * 0.5
                medium_threshold = max_dimension * 1.0
                
                if distance <= high_threshold:
                    current_lod = "High"
                elif distance <= medium_threshold:
                    current_lod = "Medium"
                else:
                    current_lod = "Low"
                
                # 只在LOD状态改变时输出信息
                if (current_lod_state["last_lod"] != current_lod or 
                    abs(current_lod_state["last_distance"] - distance) > 10.0):
                    
                    print(f"🔄 LOD switched to {current_lod} (distance: {distance:.1f})")
                    current_lod_state["last_lod"] = current_lod
                    current_lod_state["last_distance"] = distance
                    
        except Exception as e:
            print(f"Error in async update loop: {e}")
        
        # 等待一小段时间
        await asyncio.sleep(0.1)

def stop_automatic_lod_switching():
    """停止自动LOD切换"""
    global realtime_update_active, realtime_update_data, auto_update_subscription
    
    print("\n=== Stopping Automatic LOD Switching ===")
    
    # 停止自动更新
    realtime_update_active = False
    realtime_update_data = None
    
    # 取消订阅
    if auto_update_subscription:
        try:
            auto_update_subscription = None
            print("✅ Automatic LOD switching stopped successfully!")
        except Exception as e:
            print(f"Warning: Could not properly stop subscription: {e}")
    else:
        print("✅ Automatic LOD switching stopped successfully!")

def start_realtime_lod_switching():
    """启动实时LOD切换（现在使用新的自动方法）"""
    return start_automatic_lod_switching()

def stop_realtime_lod_switching():
    """停止实时LOD切换（现在使用新的自动方法）"""
    return stop_automatic_lod_switching()

def manual_lod_update():
    """手动更新LOD（用于测试）"""
    global realtime_update_active, realtime_update_data
    
    print("\n=== Manual LOD Update ===")
    
    # 如果实时更新处于活动状态，使用实时更新数据
    if realtime_update_active and realtime_update_data:
        stage = realtime_update_data['stage']
        region_bounds = realtime_update_data['region_bounds']
        usdz_paths = realtime_update_data['usdz_paths']
        
        # 执行一次LOD更新
        update_lod_visibility_by_geometric_error(stage, region_bounds, usdz_paths, verbose=False)
        
        # 获取当前相机位置和LOD状态用于状态跟踪
        camera = stage.GetPrimAtPath("/World/Camera")
        if camera:
            xformable = UsdGeom.Xformable(camera)
            transform = xformable.GetLocalTransformation()
            camera_position = Gf.Vec3f(transform.ExtractTranslation())
            
            center = region_bounds.center
            distance = math.sqrt(
                (camera_position[0] - center[0])**2 + 
                (camera_position[1] - center[1])**2 + 
                (camera_position[2] - center[2])**2
            )
            
            # 确定当前LOD级别
            region_size = region_bounds.size
            max_dimension = max(region_size[0], region_size[1], region_size[2])
            
            high_threshold = max_dimension * 0.5
            medium_threshold = max_dimension * 1.0
            
            if distance <= high_threshold:
                current_lod = "High"
            elif distance <= medium_threshold:
                current_lod = "Medium"
            else:
                current_lod = "Low"
            
            # 只在LOD状态改变时输出信息
            global current_lod_state
            if (current_lod_state["last_lod"] != current_lod or 
                abs(current_lod_state["last_distance"] - distance) > 10.0):
                
                print(f"LOD switched to {current_lod} (distance: {distance:.1f})")
                current_lod_state["last_lod"] = current_lod
                current_lod_state["last_distance"] = distance
        
        print("Manual LOD update completed!")
    else:
        # 配置USDZ文件路径
        usdz_paths = {
            "High": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/20.usdz",
            "Medium": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/18.usdz", 
            "Low": "E:/wanleqi/FlorenzVillage/data/block_0000/usdz_output/17.usdz"
        }
        
        # 获取当前stage和区域边界
        stage = omni.usd.get_context().get_stage()
        region_bounds, _ = get_nurec_bounds_from_stage()
        
        # 执行一次LOD更新
        update_lod_visibility_by_geometric_error(stage, region_bounds, usdz_paths)
        
        print("Manual LOD update completed!")

def check_current_lod_status():
    """检查当前LOD状态"""
    print("\n=== Current LOD Status ===")
    
    try:
        stage = omni.usd.get_context().get_stage()
        region_bounds, _ = get_nurec_bounds_from_stage()
        
        # 获取相机位置
        camera = stage.GetPrimAtPath("/World/Camera")
        if not camera:
            print("Camera not found!")
            return
        
        xformable = UsdGeom.Xformable(camera)
        transform = xformable.GetLocalTransformation()
        camera_position = Gf.Vec3f(transform.ExtractTranslation())
        
        # 计算距离
        center = region_bounds.center
        distance = math.sqrt(
            (camera_position[0] - center[0])**2 + 
            (camera_position[1] - center[1])**2 + 
            (camera_position[2] - center[2])**2
        )
        
        # 确定当前LOD级别
        region_size = region_bounds.size
        max_dimension = max(region_size[0], region_size[1], region_size[2])
        
        high_threshold = max_dimension * 0.5
        medium_threshold = max_dimension * 1.0
        
        if distance <= high_threshold:
            current_lod = "High"
        elif distance <= medium_threshold:
            current_lod = "Medium"
        else:
            current_lod = "Low"
        
        print(f"Camera position: {camera_position}")
        print(f"Distance to region center: {distance:.1f}")
        print(f"Current LOD level: {current_lod}")
        print(f"Distance thresholds:")
        print(f"  - High: 0-{high_threshold:.1f}")
        print(f"  - Medium: {high_threshold:.1f}-{medium_threshold:.1f}")
        print(f"  - Low: {medium_threshold:.1f}+")
        
        # 检查各LOD级别的可见性
        print(f"\nLOD visibility status:")
        lod_levels = ["High", "Medium", "Low"]
        for lod_name in lod_levels:
            lod_path = f"/World/SingleRegion/LOD_{lod_name}"
            lod_prim = stage.GetPrimAtPath(lod_path)
            
            if lod_prim:
                imageable = UsdGeom.Imageable(lod_prim)
                vis_attr = imageable.GetVisibilityAttr()
                visibility = vis_attr.Get()
                print(f"  - LOD_{lod_name}: {'Visible' if visibility == UsdGeom.Tokens.inherited else 'Hidden'}")
            else:
                print(f"  - LOD_{lod_name}: Not found")
                
    except Exception as e:
        print(f"Error checking LOD status: {e}")

def main():
    """主函数"""
    print("Simple LOD Example - Single Region with Actual USDZ Files")
    print("=" * 60)
    
    try:
        simple_interactive_demo()
        # while True:
        #     print("\nAvailable options:")
        #     print("1. Start automatic LOD switching (recommended)")
        #     print("2. Start real-time LOD switching (legacy)")
        #     print("3. Simple interactive demo")
        #     print("4. Manual LOD update (one-time)")
        #     print("5. Check current LOD status")
        #     print("6. Stop automatic/real-time LOD switching")
        #     print("0. Exit")
            
        #     try:
        #         choice = input("\nPlease select an option (0-6): ").strip()
                
        #         if choice == "0":
        #             print("Exiting...")
        #             break
        #         elif choice == "1":
        #             # 启动自动LOD切换
        #             stage, region_bounds, scheduler = start_automatic_lod_switching()
                    
        #             print("\n" + "=" * 50)
        #             print("Automatic LOD switching started successfully!")
        #             print("\nFeatures:")
        #             print("- True automatic LOD switching based on camera distance")
        #             print("- Multiple fallback methods for maximum compatibility")
        #             print("- Real-time updates as you move the camera")
        #             print("- Coordinate system correction applied")
        #             print("- Uses actual USDZ files with visibility control")
        #             print("\nTo stop automatic switching, select option 6")
        #             print("To check current status, select option 5")
                    
        #         elif choice == "2":
        #             # 启动实时LOD切换（旧版本）
        #             stage, region_bounds, scheduler = start_realtime_lod_switching()
                    
        #             print("\n" + "=" * 50)
        #             print("Real-time LOD switching started successfully!")
        #             print("\nFeatures:")
        #             print("- Automatic LOD switching based on camera distance")
        #             print("- Real-time updates as you move the camera")
        #             print("- Coordinate system correction applied")
        #             print("- Uses actual USDZ files with visibility control")
        #             print("\nTo stop real-time switching, select option 6")
        #             print("To check current status, select option 5")
                    
        #         elif choice == "3":
        #             # 简单交互式演示
        #             simple_interactive_demo()
                    
        #         elif choice == "4":
        #             # 手动更新LOD
        #             manual_lod_update()
                    
        #         elif choice == "5":
        #             # 检查当前LOD状态
        #             check_current_lod_status()
                    
        #         elif choice == "6":
        #             # 停止自动/实时LOD切换
        #             stop_automatic_lod_switching()
                    
        #         else:
        #             print("Invalid choice. Please select a number between 0-6.")
        #             continue
        #     except KeyboardInterrupt:
        #         print("\n\nInterrupted by user. Exiting...")
        #         break
        #     except Exception as e:
        #         print(f"Error in option {choice}: {e}")
        #         continue
        
        # print("\n" + "=" * 50)
        # print("Simple LOD example completed successfully!")
        # print("\nSummary:")
        # print("- Single region with 3 LOD levels (High, Medium, Low)")
        # print("- LOD switching based on camera distance")
        # print("- Distance thresholds dynamically calculated from actual NUREC bounds")
        # print("- Coordinate system correction applied (90, 180, 0) rotation")
        # print("- Total: 3 LOD tiles for one region")
        # print("- LOD switching through visibility control")
        # print("\nNext steps:")
        # print("1. 修改usdz_paths中的路径为您的实际USDZ文件路径")
        # print("2. 运行相应的测试函数")
        
    except Exception as e:
        print(f"Example failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

# 便捷函数 - 用户可以直接调用这些函数
def start_lod():
    """便捷函数：启动自动LOD切换"""
    return start_automatic_lod_switching()

def start_auto_lod():
    """便捷函数：启动自动LOD切换"""
    return start_automatic_lod_switching()

def stop_lod():
    """便捷函数：停止自动LOD切换"""
    return stop_automatic_lod_switching()

def check_status():
    """便捷函数：检查当前LOD状态"""
    return check_current_lod_status()

def manual_update():
    """便捷函数：手动更新LOD"""
    return manual_lod_update()

def demo():
    """便捷函数：运行交互式演示"""
    return simple_interactive_demo()

def test():
    """便捷函数：运行测试"""
    return test_distance_based_lod()

# 使用说明
print("\n" + "=" * 60)
print("Simple LOD Example - Usage Instructions")
print("=" * 60)
print("\nYou can use this script in two ways:")
print("\n1. Interactive Menu (recommended):")
print("   - Run the script and follow the menu prompts")
print("   - Select options 0-6 to navigate")
print("\n2. Direct Function Calls:")
print("   - start_lod()      - Start automatic LOD switching")
print("   - start_auto_lod() - Start automatic LOD switching")
print("   - stop_lod()       - Stop automatic LOD switching")
print("   - check_status()   - Check current LOD status")
print("   - manual_update()  - Manual LOD update")
print("   - demo()           - Run interactive demo")
print("   - test()           - Run distance-based test")
print("\nAutomatic Update Methods:")
print("   - USD Event System: Updates when camera changes")
print("   - Timeline Events: Updates every frame")
print("   - Timer-based: Updates every 0.1 seconds")
print("   - Async Loop: Continuous updates")
print("\nExample usage:")
print("   start_lod()  # Start automatic switching")
print("   check_status()  # Check current status")
print("   stop_lod()  # Stop automatic switching")
print("\nNote: The system will automatically try multiple methods")
print("      to ensure LOD switching works in your environment.")
print("=" * 60) 